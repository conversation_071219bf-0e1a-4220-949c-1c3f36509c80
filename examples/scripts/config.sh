# 关于oss挂载详细参考手册：https://aliyuque.antfin.com/uxctvg/gh8c24/xmbycq#Rw6B3
## oss bucket挂载到本地的路径是/data/oss_bucket_0/
## 如果要访问原bucket中oss path为：oss://your_bucket/dir1/dir2/file_name的文件，
## 对应的本地挂载路径是：/data/oss_bucket_0/dir1/dir2/file_name

OSS_ACCESS_ID="LTAI5tNjgHhxMDKEb14JeinK"
OSS_ACCESS_KEY="******************************"
OSS_ENDPOINT="oss-cn-zhangjiakou-internal.aliyuncs.com"
OSS_BUCKET="liushi-dev"

# set this when you use odps
#ODPS_PROJECT=""
#ODPS_TABLE=""

# 如果你用到 OpenLM Datasets 请先申请 Token
# https://aliyuque.antfin.com/alimama-nebula/vbw69h/tkwsyrgee6rof6po
OPENLM_TOKEN=""

QUEUE="ai_assistant_l20x"
