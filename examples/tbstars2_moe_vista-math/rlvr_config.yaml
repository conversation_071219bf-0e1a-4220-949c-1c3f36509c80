defaults:
  - ../config/deepspeed_zero@_here_
  - ../config/deepspeed_zero2@_here_
  - ../config/deepspeed_zero3@_here_
  - ../config/deepspeed_zero3_cpuoffload@_here_

hydra:
  run:
    dir: .
  output_subdir: null

exp_name: "tbstars2_moe_vista_math_config"
seed: 42
logging_dir: ./output/logs
output_dir: ./output

checkpoint_config:
  type: file_system
  output_dir: /data/cpfs_0/shidie/models

track_with: tensorboard
tracker_kwargs:
  log_dir: /data/oss_bucket_0/shidie/llm/tensorboard

save_steps: 20
logging_steps: 1
eval_steps: 1
resume_from_checkpoint: false

rollout_batch_size: 512
num_return_sequences_in_group: 8
is_num_return_sequences_expand: true
prompt_length: 1024
response_length: 4096
generate_opt_level: 0

ppo_epochs: 1
value_clip: 0.5
reward_clip: 10
advantage_clip: 10.0
whiten_advantages: false
init_kl_coef: 0.0
adv_estimator: "grpo"
use_kl_loss: true
kl_loss_coef: 1.0e-2

pretrain: model.turing_algo.TBStars2-MoE-VL-15A1_5-32K-Instruct/version=0613
# pretrain: /data/cpfs_0/common/models/TBStars2-MoE-VL-15A1_5-Instruct

actor_train:
  model_args:
    disable_gradient_checkpointing: false
    dtype: bf16
    model_type: ~
  training_args:
    learning_rate: 1.0e-6
    weight_decay: 1.0e-2
    per_device_train_batch_size: 4
    gradient_accumulation_steps: 64
    warmup_steps: 0
    num_train_epochs: 50
  data_args:
    template: qwen2_5
    # use leonardPKU/GEOQA_R1V_Train_8K as dataset
    # download to ./data/geoqa_data from https://huggingface.co/datasets/leonardPKU/GEOQA_R1V_Train_8K
    file_name: ./data/geoqa_data/
    dataset_dir: ./data
    preprocessing_num_workers: 16
  strategy_args:
    strategy_name: megatron_train
    strategy_config:
      sequence_parallel: true
      tensor_model_parallel_size: 2
      context_parallel_size: 1
      expert_model_parallel_size: 4
      pipeline_model_parallel_size: 1
      overlap_grad_reduce: false
      use_distributed_optimizer: true
      moe_grouped_gemm: true
      moe_shared_expert_overlap: true
      moe_layer_recompute: true
      transformer_impl: transformer_engine
      bf16: true
  device_mapping: list(range(0,8))
  infer_batch_size: 8

actor_infer:
  model_args:
    disable_gradient_checkpointing: true
    dtype: bf16
  generating_args:
    max_new_tokens: ${response_length}
    top_p: 0.99
    top_k: 100
    num_beams: 1
    temperature: 0.99
    num_return_sequences: ${num_return_sequences_in_group}
  data_args:
    template: qwen2_5
  strategy_args:
    strategy_name: vllm
    strategy_config:
      gpu_memory_utilization: 0.7
      block_size: 16
  num_gpus_per_worker: 1
  device_mapping: list(range(0,8))
  infer_batch_size: 32

reference:
  model_args:
    disable_gradient_checkpointing: true
    dtype: bf16
    # In transformers>=4.50.0, if model.from_pretrained with auto device_map, None
    # tp_plan (and tp_plan of model is not None) and WORLD_SIZE>1, TP would be used.
    # Thus using device_map=0 to disable HF transformers parallel, otherwise use
    # zero3 for reference model
    device_map: "cuda:0"
    model_type: ~
  data_args:
    template: qwen2_5
  strategy_args:
    strategy_name: megatron_infer
    strategy_config:
      sequence_parallel: true
      tensor_model_parallel_size: 1
      context_parallel_size: 1
      pipeline_model_parallel_size: 1
      expert_model_parallel_size: 1
      moe_grouped_gemm: true
      moe_shared_expert_overlap: true
      bf16: true
  device_mapping: list(range(0,8))
  infer_batch_size: 1

rewards:
  math_rule:
    # vl pipeline support MathRuleRewardWorker only, at present.
    # Support for rewards in other domains will be retained for future implementation.
    worker_cls: roll.pipeline.rlvr.rewards.math_rule_reward_worker.MathRuleRewardWorker
    model_args:
      model_name_or_path: ${pretrain}
    data_args:
      template: qwen2_5
    strategy_args:
      strategy_name: hf_infer
      strategy_config: ~
    world_size: 8
    infer_batch_size: 4
