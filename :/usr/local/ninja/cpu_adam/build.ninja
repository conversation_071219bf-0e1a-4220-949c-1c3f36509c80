ninja_required_version = 1.3
cxx = c++

cflags = -DTORCH_EXTENSION_NAME=cpu_adam -DTORCH_API_INCLUDE_EXTENSION_H -DPYBIND11_COMPILER_TYPE=\"_gcc\" -DPYBIND11_STDLIB=\"_libstdcpp\" -DPYBIND11_BUILD_ABI=\"_cxxabi1011\" -I/opt/conda/envs/roll_env/lib/python3.10/site-packages/deepspeed/ops/csrc/includes -isystem /opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/include -isystem /opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/include/torch/csrc/api/include -isystem /opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/include/TH -isystem /opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/include/THC -isystem /opt/conda/envs/roll_env/include/python3.10 -D_GLIBCXX_USE_CXX11_ABI=0 -fPIC -std=c++17 -O3 -std=c++17 -g -Wno-reorder -L/usr/local/cuda/lib64 -lcudart -lcublas -g -march=native -fopenmp -D__AVX512__ -D__ENABLE_CUDA__ -DBF16_AVAILABLE
post_cflags = 
cuda_dlink_post_cflags = 
ldflags = -shared -lcurand -L/usr/local/cuda/lib64 -L/opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/lib -lc10 -ltorch_cpu -ltorch -ltorch_python

rule compile
  command = $cxx -MMD -MF $out.d $cflags -c $in -o $out $post_cflags
  depfile = $out.d
  deps = gcc



rule link
  command = $cxx $in $ldflags -o $out

build cpu_adam.o: compile /opt/conda/envs/roll_env/lib/python3.10/site-packages/deepspeed/ops/csrc/adam/cpu_adam.cpp
build cpu_adam_impl.o: compile /opt/conda/envs/roll_env/lib/python3.10/site-packages/deepspeed/ops/csrc/adam/cpu_adam_impl.cpp



build cpu_adam.so: link cpu_adam.o cpu_adam_impl.o

default cpu_adam.so
