ninja_required_version = 1.3
cxx = c++
nvcc = /usr/local/cuda/bin/nvcc

cflags = -DTORCH_EXTENSION_NAME=fused_adam -DTORCH_API_INCLUDE_EXTENSION_H -DPYBIND11_COMPILER_TYPE=\"_gcc\" -DPYBIND11_STDLIB=\"_libstdcpp\" -DPYBIND11_BUILD_ABI=\"_cxxabi1011\" -I/opt/conda/envs/roll_env/lib/python3.10/site-packages/deepspeed/ops/csrc/includes -I/opt/conda/envs/roll_env/lib/python3.10/site-packages/deepspeed/ops/csrc/adam -isystem /opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/include -isystem /opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/include/torch/csrc/api/include -isystem /opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/include/TH -isystem /opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/include/THC -isystem /usr/local/cuda/include -isystem /opt/conda/envs/roll_env/include/python3.10 -D_GLIBCXX_USE_CXX11_ABI=0 -fPIC -std=c++17 -O3 -std=c++17 -g -Wno-reorder -DVERSION_GE_1_1 -DVERSION_GE_1_3 -DVERSION_GE_1_5 -DBF16_AVAILABLE
post_cflags = 
cuda_cflags = -DTORCH_EXTENSION_NAME=fused_adam -DTORCH_API_INCLUDE_EXTENSION_H -DPYBIND11_COMPILER_TYPE=\"_gcc\" -DPYBIND11_STDLIB=\"_libstdcpp\" -DPYBIND11_BUILD_ABI=\"_cxxabi1011\" -I/opt/conda/envs/roll_env/lib/python3.10/site-packages/deepspeed/ops/csrc/includes -I/opt/conda/envs/roll_env/lib/python3.10/site-packages/deepspeed/ops/csrc/adam -isystem /opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/include -isystem /opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/include/torch/csrc/api/include -isystem /opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/include/TH -isystem /opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/include/THC -isystem /usr/local/cuda/include -isystem /opt/conda/envs/roll_env/include/python3.10 -D_GLIBCXX_USE_CXX11_ABI=0 -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -gencode=arch=compute_80,code=compute_80 -gencode=arch=compute_80,code=sm_80 --compiler-options '-fPIC' -O3 -DVERSION_GE_1_1 -DVERSION_GE_1_3 -DVERSION_GE_1_5 -lineinfo --use_fast_math -gencode=arch=compute_80,code=sm_80 -gencode=arch=compute_80,code=compute_80 -DBF16_AVAILABLE -U__CUDA_NO_BFLOAT16_OPERATORS__ -U__CUDA_NO_BFLOAT162_OPERATORS__ -U__CUDA_NO_BFLOAT16_CONVERSIONS__ -std=c++17
cuda_post_cflags = 
cuda_dlink_post_cflags = 
ldflags = -shared -L/opt/conda/envs/roll_env/lib/python3.10/site-packages/torch/lib -lc10 -lc10_cuda -ltorch_cpu -ltorch_cuda -ltorch -ltorch_python -L/usr/local/cuda/lib64 -lcudart

rule compile
  command = $cxx -MMD -MF $out.d $cflags -c $in -o $out $post_cflags
  depfile = $out.d
  deps = gcc

rule cuda_compile
  depfile = $out.d
  deps = gcc
  command = $nvcc --generate-dependencies-with-compile --dependency-output $out.d $cuda_cflags -c $in -o $out $cuda_post_cflags



rule link
  command = $cxx $in $ldflags -o $out

build fused_adam_frontend.o: compile /opt/conda/envs/roll_env/lib/python3.10/site-packages/deepspeed/ops/csrc/adam/fused_adam_frontend.cpp
build multi_tensor_adam.cuda.o: cuda_compile /opt/conda/envs/roll_env/lib/python3.10/site-packages/deepspeed/ops/csrc/adam/multi_tensor_adam.cu



build fused_adam.so: link fused_adam_frontend.o multi_tensor_adam.cuda.o

default fused_adam.so
