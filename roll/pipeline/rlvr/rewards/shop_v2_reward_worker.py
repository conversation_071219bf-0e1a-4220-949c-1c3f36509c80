from typing import Optional, Union, Dict, List, Any, Tu<PERSON>, Set
import json
import re
import torch
import requests
import time
import traceback
import numpy as np
from functools import partial
import tensordict
from tensordict import TensorDict
from roll.configs.worker_config import WorkerConfig
from roll.distributed.executor.worker import Worker
from roll.distributed.scheduler.decorator import Dispatch, register
from roll.distributed.scheduler.protocol import DataProto
from roll.distributed.strategy.factory import create_strategy
from roll.distributed.strategy.strategy import InferenceStrategy, TrainStrategy
from roll.models.model_providers import default_tokenizer_provider, default_reward_model_provider
from roll.utils.logging import get_logger
from roll.utils.context_managers import state_offload_manger
from roll.utils.prompt import *
from roll.datasets.chat_template import get_chat_template
import ray


@ray.remote
class ShopV2RewardWorker(Worker):
    """
    Reward Worker that uses LLM-as-judge to compute rewards for shopping cart tasks.
    """

    def __init__(self, worker_config: WorkerConfig):
        super().__init__(worker_config=worker_config)
        self.rank_info.dp_rank = self.rank_info.rank
        self.rank_info.dp_size = self.rank_info.world_size
        self.tokenizer = None
        self.strategy: Optional[Union[InferenceStrategy, TrainStrategy]] = None

        # Shopping cart LLM judge相关配置
        self.judge_prompt = self.worker_config.judge_prompt if hasattr(self.worker_config, "judge_prompt") else None
        self.judge_model_type = (
            self.worker_config.judge_model_type if hasattr(self.worker_config, "judge_model_type") else "api"
        )
        self.judge_model_name = (
            self.worker_config.judge_model_name if hasattr(self.worker_config, "judge_model_name") else "claude_opus4"
        )

        # 初始化OpenAI客户端
        self.client = None

    @register(dispatch_mode=Dispatch.ONE_TO_ALL)
    def initialize(self, pipeline_config):
        super().initialize(pipeline_config)
        if self.judge_model_type == "api":
            from openai import OpenAI
            self.client = OpenAI(
                api_key='27db1fc058c1861870be4c21a7f93cdc',
                base_url="https://idealab.alibaba-inc.com/api/openai/v1",
            )
            self.tokenizer = default_tokenizer_provider(model_args=self.worker_config.model_args)
            print(f"{self.worker_name} initialized with API model")

        elif self.judge_model_type == "inference":
            self.strategy = create_strategy(worker=self)
            self.strategy.initialize(model_provider=default_reward_model_provider)
            self.tokenizer = self.strategy.tokenizer
            print(f"{self.worker_name} initialized with inference model")
            self.strategy.offload_states()
        else:
            raise ValueError(f"Unsupported model type: {self.judge_model_type}")

    def _call_api_model(self, messages: List[Dict], retry_times=3) -> str:
        output = ""
        if not self.client:
            raise ValueError("OpenAI client not initialized")
        
        while retry_times > 0:
            retry_times -= 1
            try:
                completion = self.client.chat.completions.create(
                    model="claude_opus4",
                    messages=messages,
                    temperature=0.1,
                )
                
                output = completion.choices[0].message.content
                
                if output is not None and output != "":
                    break
            except Exception as e:
                print(f"API call error: {e}")
                continue
        
        self.logger.info(f"shopping cart judge model api output: {str(output)}")
        return output

    def _run_local_inference(self, messages: Dict) -> str:
        if not self.strategy:
            raise ValueError("Strategy not initialized for local inference")

        template_name = self.worker_config.data_args.template
        chat_template_func = get_chat_template(template_name, self.tokenizer)
        text = chat_template_func(messages)

        tokenized = self.tokenizer(text, return_tensors="pt")
        input_ids = tokenized["input_ids"].to("cuda")
        attention_mask = tokenized["attention_mask"].to("cuda")

        generation_config = self.worker_config.generating_args.to_dict()
        generation_config["eos_token_id"] = [self.tokenizer.eos_token_id]
        generation_config["pad_token_id"] = self.tokenizer.pad_token_id

        data = DataProto(
            batch=TensorDict({"input_ids": input_ids, "attention_mask": attention_mask}, batch_size=input_ids.shape[0])
        )
        data = data.to("cuda")
        data.meta_info = {"micro_batch_size": self.worker_config.infer_batch_size}

        with torch.no_grad():
            output = self.strategy.generate(batch=data, generation_config=generation_config)
            if isinstance(output, torch.Tensor):
                generate_ids = output[:, len(input_ids[0]) :]
            else:
                generate_ids = output.batch["input_ids"][:, len(input_ids[0]) :]

        output = self.tokenizer.decode(generate_ids[0], skip_special_tokens=True)
        self.logger.info(f"shopping cart judge model inference output: {str(output)}")
        return output.strip()

    def _check_hallucination(self, answer_content: str, product_info: Dict = None, user_history: Dict = None) -> Tuple[bool, str]:
        """
        检查模型输出是否存在幻觉
        
        :param answer_content: 模型的回答内容
        :param product_info: 商品信息
        :param user_history: 用户历史行为
        :return: (float, str) - 幻觉评分(0-1)，检查结果描述
        """
        try:
            system_prompt = """你是一位专业的AI输出质量检测专家，需要判断模型输出是否存在幻觉。
幻觉定义：模型输出中包含输入信息中不存在的内容，且无法从输入信息中合理推导得出。

请仔细分析商品信息和用户历史行为，判断模型输出属于以下哪种情况：
- 输入信息包括商品参数与商品用户评价
1. 【严重幻觉】模型输出包含输入信息中不存在的具体内容
2. 【内容矛盾】模型输出与输入信息明确矛盾或冲突
3. 【无幻觉】模型输出完全基于输入信息或合理推导

【重要】以下情况不视为幻觉：
- 基于输入信息的合理推导和估算
- 对商品属性的主观描述和评价
- 基于用户历史行为的合理推断
- 来自商品用户评价的内容

请只回答以下三种情况之一，不要添加任何解释或理由：
- 严重幻觉
- 内容矛盾
- 无幻觉"""

            # 构建用户提示
            user_prompt = "请判断以下模型输出是否存在幻觉：\n\n"
            
            # 添加商品信息
            if product_info:
                user_prompt += "【输入商品信息】\n"
                user_prompt += json.dumps(product_info, ensure_ascii=False) + "\n\n"
            
            # 添加用户历史行为
            if user_history:
                user_prompt += "【用户历史行为】\n"
                user_prompt += json.dumps(user_history, ensure_ascii=False) + "\n\n"
            
            # 添加模型回答内容
            user_prompt += "【模型回答内容】\n"
            user_prompt += answer_content + "\n\n"
            
            user_prompt += "请判断上述模型回答内容属于哪种情况，只需回答'严重幻觉'、'内容矛盾'或'无幻觉'，并简要说明理由。"
            
            # 调用API检查幻觉
            if self.judge_model_type == "api":
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
                llm_response = self._call_api_model(messages)
            else:
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
                llm_response = self._run_local_inference(messages)
            
            # 解析LLM响应并分配分数
            if "无幻觉" in llm_response:
                score = 1.0
                return score, f"{llm_response}"
            elif "内容矛盾" in llm_response:
                score = -0.5
                return score, f"{llm_response}"
            elif "严重幻觉" in llm_response:
                score = -0.5
                return score, f"{llm_response}"
            else:
                # 默认情况，无法明确判断
                score = 0.0
                return score, f"无法明确判断: {llm_response}"
                
        except Exception as e:
            self.logger.error(f"检查幻觉时出错: {e}")
            return 0.0, f"幻觉检查失败: {str(e)}"
    
    def _check_ans_quality(self, answer_content: str, product_info: Dict = None, user_history: Dict = None) -> Tuple[float, str]:
        """
        从主观角度对answer_content的内容质量进行评价

        :param answer_content: 模型的回答内容
        :param product_info: 商品信息
        :param user_history: 用户历史行为
        :return: (float, str) - 质量评分(0-1)，检查结果描述
        """
        try:
            system_prompt = """你是一位专业的购物推荐内容质量评估专家，需要从主观角度评价选购建议和对比表格的内容质量。

请仔细分析选购建议和对比表格，判断内容质量属于以下哪种情况：

【选购建议评价标准】
1. 场景表述要贴近用户实际需求，避免低频场景、引发用户体验失真、价值观风险或歧义模糊的表述
2. 选购建议要明确有观点，避免模棱两可，需要日常口语化
3. 结合用户历史行为进行个性化推荐，但避免性别刻板印象，注重用户隐私保护
4. 选购建议中的结论与对比表格的商品信息需要观点一致，避免产生冲突
5. 如果对比商品在某维度上信息一致，应该避免在选购建议中进行对比或者拉踩
6. 如果对比商品中某商品明显有优势，应该在选购建议中突出优势

【表格评价标准】
1. 表格维度上禁止出现emoji、表情符号等非文字信息
2. 表格中禁止出现价格和店铺相关维度
3. 表格的商品排列顺序必须与输入的商品顺序一致
4. 表格中避免对比值相同的维度，相同的维度应该删掉
5. 表格内容需要严格遵循输入的商品信息
6. 商品明显存在优势的维度需要加粗提醒
7. 专业术语需要增加简洁、直观、易懂的解释

【质量等级】
1. 【优秀质量】选购建议自然流畅，场景贴近实际，表格规范准确，个性化推荐合理
2. 【良好质量】选购建议基本合理，表格大部分规范，存在少量可优化点
3. 【一般质量】选购建议或表格存在明显问题，但整体可用
4. 【较差质量】选购建议不合理或表格存在严重错误

请只回答以下四种情况之一，不要添加任何解释或理由：
- 优秀质量
- 良好质量
- 一般质量
- 较差质量"""

            # 构建用户提示
            user_prompt = "请评价以下选购建议和对比表格的内容质量：\n\n"

            # 添加商品信息
            if product_info:
                user_prompt += "【输入商品信息】\n"
                user_prompt += json.dumps(product_info, ensure_ascii=False) + "\n\n"

            # 添加用户历史行为
            if user_history:
                user_prompt += "【用户历史行为】\n"
                user_prompt += json.dumps(user_history, ensure_ascii=False) + "\n\n"

            # 添加模型回答内容
            user_prompt += "【选购建议和对比表格】\n"
            user_prompt += answer_content + "\n\n"

            user_prompt += "请评价上述内容的质量等级，只回答以下四个选项之一，不要添加任何其他内容：\n优秀质量\n良好质量\n一般质量\n较差质量"

            # 调用API检查质量
            if self.judge_model_type == "api":
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
                llm_response = self._call_api_model(messages)
            else:
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
                llm_response = self._run_local_inference(messages)

            # 解析LLM响应并分配分数
            if "优秀质量" in llm_response:
                score = 1.0
                return score, f"优秀质量: {llm_response}"
            elif "良好质量" in llm_response:
                score = 0.8
                return score, f"良好质量: {llm_response}"
            elif "一般质量" in llm_response:
                score = 0.5
                return score, f"一般质量: {llm_response}"
            elif "较差质量" in llm_response:
                score = 0.2
                return score, f"较差质量: {llm_response}"
            else:
                # 默认情况，无法明确判断
                score = 0.5
                return score, f"无法明确判断: {llm_response}"

        except Exception as e:
            self.logger.error(f"检查内容质量时出错: {e}")
            return 0.0, f"质量检查失败: {str(e)}"

    def _check_format(self, answer_content: str, product_info: Dict = None) -> Tuple[float, str]:
        """
        检查answer_content的格式是否符合要求
        
        :param answer_content: 模型的回答内容
        :param product_info: 商品信息字典
        :return: (float, str) - 格式评分(0-1)，检查结果描述
        """
        try:
            # (1) 检查是否只有一个summary和一个table
            summary_count = answer_content.count('<summary>')
            summary_end_count = answer_content.count('</summary>')
            table_count = answer_content.count('<table>')
            table_end_count = answer_content.count('</table>')
       
            if summary_count != 1 or summary_end_count != 1:
                return -2.0, f"summary标签数量错误: <summary>={summary_count}, </summary>={summary_end_count}"
      
            if table_count != 1 or table_end_count != 1:
                return -2.0, f"table标签数量错误: <table>={table_count}, </table>={table_end_count}"
           
            # 检查基本格式：<summary>...</summary><table>...</table>
            # pattern = r"^<summary>.*?</summary><table>.*?</table>$"
            pattern = r"^<summary>[\s\S]*?</summary>\s*<table>[\s\S]*?</table>$"
            if not re.match(pattern, answer_content.strip(), re.DOTALL):
                return -2.0, "基本格式错误: 不符合<summary>...</summary><table>...</table>格式"
            
            # (2) 检查商品数量和表格列数是否一致
            if product_info:
                expected_product_count = len([k for k in product_info.keys() if k.startswith('商品') and k.endswith('信息')])
                
                # 提取表格内容
                table_match = re.search(r'<table>\s*(.*?)\s*</table>', answer_content, re.DOTALL)
                if not table_match:
                    return -1.0, "无法提取表格内容"
                
                table_content = table_match.group(1).strip()
                
                # 查找表头行
                lines = table_content.split('\n')
                header_line = None
                for line in lines:
                    line = line.strip()
                    if line.startswith('| 对比维度'):
                        header_line = line
                        break
               
                if not header_line:
                    return -1.0, "表格缺少'对比维度'表头"
                
                # 计算表格列数
                actual_columns = header_line.count('|') - 1
                expected_columns = expected_product_count + 1  # 商品数量 + 对比维度列
               
                if actual_columns != expected_columns:
                    return -1.0, f"表格列数不匹配: 期望{expected_columns}列，实际{actual_columns}列"
                
                # (3) 检查商品顺序是否一致
                order_check_result = self._check_product_order(answer_content, product_info)
                if not order_check_result[0]:
                    return -0.8, f"商品顺序错误: {order_check_result[1]}"
               
                # (4) 检查是否有重复或全空的对比维度
                duplicate_check_result = self._check_duplicate_dimensions(table_content, product_info)
                if not duplicate_check_result[0]:
                    return -0.5, f"表格内容错误: {duplicate_check_result[1]}"
            
            return 1.0, "格式完全正确"
                    
        except Exception as e:
            self.logger.error(f"格式检查时出错: {e}")
            return 0.0, f"格式检查失败: {str(e)}"

    def _check_product_order(self, answer_content: str, product_info: Dict) -> Tuple[bool, str]:
        """检查表格中商品顺序是否与输入数据一致"""
        try:
            # 提取商品标题
            product_titles = []
            for i in range(1, 4):
                key = f'商品{i}信息'
                if key in product_info:
                    try:
                        info = json.loads(product_info[key]) if isinstance(product_info[key], str) else product_info[key]
                        title = info.get('标题', '') or info.get('name', '')
                        if title:
                            product_titles.append(title)
                    except:
                        continue
            
            # 提取表格中的商品名称
            table_match = re.search(r'<table>\s*(.*?)\s*</table>', answer_content, re.DOTALL)
            if not table_match:
                return False, "无法提取表格内容"
            
            table_content = table_match.group(1).strip()
            lines = table_content.split('\n')
            
            header_line = None
            for line in lines:
                line = line.strip()
                if line.startswith('| 对比维度'):
                    header_line = line
                    break
            
            if not header_line:
                return False, "找不到表头行"
            
            # 解析表头，提取商品名称
            columns = [col.strip() for col in header_line.split('|')]
            table_names = [col for col in columns[1:] if col and col != '对比维度']
            
            if len(product_titles) != len(table_names):
                return False, f"商品数量不匹配: 输入{len(product_titles)}个，表格{len(table_names)}个"
            
            # 检查顺序一致性（使用简单的包含关系检查）
            for i, (title, table_name) in enumerate(zip(product_titles, table_names)):
                # 简化的匹配逻辑：检查表格名称是否包含在商品标题中
                if not any(word in title for word in table_name.split() if len(word) > 1):
                    return False, f"商品{i+1}顺序不匹配: '{title[:20]}...' vs '{table_name}'"
            
            return True, "商品顺序正确"
            
        except Exception as e:
            return False, f"检查商品顺序时出错: {str(e)}"

    def _check_duplicate_dimensions(self, table_content: str, product_info: Dict) -> Tuple[bool, str]:
        """检查表格中是否有重复或全空的对比维度"""
        try:
            lines = table_content.split('\n')
            
            # 找到数据行（跳过表头和分隔行）
            data_rows = []
            found_separator = False
            for line in lines:
                line = line.strip()
                if line.startswith('| 对比维度'):
                    continue
                elif line.startswith('| :') or line.startswith('|:'):
                    found_separator = True
                    continue
                elif found_separator and line.startswith('|'):
                    data_rows.append(line)
            
            if not data_rows:
                return True, "表格无数据行"
            
            # 检查每一行的商品列是否都相同或都为空
            invalid_rows = []
            for row in data_rows:
                columns = [col.strip() for col in row.split('|')[1:-1]]
                if len(columns) < 2:
                    continue
                
                # 获取商品列（跳过第一列的维度名称）
                product_columns = columns[1:]
                
                # 检查是否都为空（包括--表示的空值）
                is_all_empty = all(not col or col == '--' or re.sub(r'[\*\-\s]', '', col) == '' for col in product_columns)
                
                # 检查是否完全相同
                is_all_same = len(set(product_columns)) == 1
                
                if is_all_empty or is_all_same:
                    dimension_name = columns[0] if columns else "未知维度"
                    invalid_rows.append(dimension_name)
            
            if invalid_rows:
                return False, f"存在重复或全空的对比维度: {', '.join(invalid_rows)}"
            
            return True, "对比维度检查通过"
            
        except Exception as e:
            return False, f"检查对比维度时出错: {str(e)}"

    def _extract_answer_content(self, response_text: str) -> tuple:
        """
        从actor模型的response中提取reasoning_content和answer_content部分
        首先检查是否符合基本格式要求："<think>...</think><summary>...</summary><table>...</table>"
        如果找不到<summary>标签，则寻找"### 选购建议"作为备选方案

        :param response_text: actor模型的完整输出
        :return: (is_valid_format, reasoning_content, answer_content)
                is_valid_format: 是否符合基本格式
                reasoning_content: <think>...</think>内容
                answer_content: <summary>...</summary><table>...</table>内容 或 ### 选购建议...内容
        """
        try:
            response_text = response_text.strip()

            # 首先尝试标准格式：<think>...</think><summary>...</summary><table>...</table>
            required_tags = ['<think>', '</think>', '<summary>', '</summary>', '<table>', '</table>']
            has_all_tags = all(tag in response_text for tag in required_tags)

            if has_all_tags:
                # 检查标签数量是否正确（每个标签应该只出现一次）
                tags_count_correct = all(response_text.count(tag) == 1 for tag in required_tags)

                if tags_count_correct:
                    # 检查标签顺序是否正确
                    think_start = response_text.find('<think>')
                    think_end = response_text.find('</think>')
                    summary_start = response_text.find('<summary>')
                    summary_end = response_text.find('</summary>')
                    table_start = response_text.find('<table>')
                    table_end = response_text.find('</table>')

                    # 验证标签顺序
                    if think_start < think_end < summary_start < summary_end < table_start < table_end:
                        # 提取reasoning_content（<think>...</think>内容）
                        reasoning_content = response_text[think_start + 7:think_end].strip()  # 7 = len('<think>')

                        # 提取answer_content（<summary>...</summary><table>...</table>内容）
                        answer_content = response_text[summary_start:table_end + 8].strip()  # 8 = len('</table>')

                        # 检查内容是否为空
                        if reasoning_content and answer_content:
                            return True, reasoning_content, answer_content

            # # for debugging 备选方案：寻找<think>...</think>和### 选购建议
            # if '### 选购建议' in response_text:
            #     advice_start = response_text.find('### 选购建议')

               
            #     reasoning_content = response_text[:advice_start].lstrip('/think\n')

            #     answer_content = response_text[advice_start:].strip()

            #     # 检查内容是否为空
            #     if answer_content:
            #         self.logger.info("使用备选格式: ### 选购建议...")
            #         return True, reasoning_content, answer_content

            # 如果都不符合，返回失败
            self.logger.warning("Response不符合任何已知格式")
            return False, "", ""

        except Exception as e:
            self.logger.warning(f"提取内容失败: {e}")
            return False, "", ""

    @register(dispatch_mode=Dispatch.DP_MP_COMPUTE, clear_cache=False)
    def compute_rewards(self, data: DataProto):
        global_step = data.meta_info.get("global_step", 0)
        is_offload_states = data.meta_info.get("is_offload_states", True)
        metrics = {}

        if self.judge_model_type == "inference" and self.strategy:
            with state_offload_manger(
                strategy=self.strategy,
                metrics=metrics,
                metric_infix=f"{self.cluster_name}/compute_rewards",
                is_offload_states=is_offload_states,
            ):
                return self._compute_rewards_impl(data, metrics)
        else:
            return self._compute_rewards_impl(data, metrics)
    
    def custom_batch_decode(
        self,
        sequences,
        skip_special_tokens: bool = True,
        keep_special_tokens: Set[str] = None,
        **kwargs,
    ) -> List[str]:
        """只跳过指定special tokens以外的其他special tokens"""
        if keep_special_tokens is None:
            keep_special_tokens = {'<summary>', '</summary>', '<table>', '</table>'}
        
        # 先完全解码（保留所有special tokens）
        result = [self.tokenizer.decode(seq, skip_special_tokens=False, **kwargs) for seq in sequences]
        
        if skip_special_tokens:
            # 手动移除不需要的special tokens
            for i, text in enumerate(result):
                for special_token in self.tokenizer.all_special_tokens:
                    if special_token not in keep_special_tokens:
                        text = text.replace(special_token, '')
                # 清理多余空格
                result[i] = ' '.join(text.split())
        
        return result

    def _compute_rewards_impl(self, data: DataProto, metrics: Dict):
        """计算购物车任务的奖励，包含基本格式检查、格式检查和幻觉检测"""
        prompts_text_list = self.tokenizer.batch_decode(data.batch["prompts"], skip_special_tokens=True)
        response_text_list = self.tokenizer.batch_decode(data.batch["responses"], skip_special_tokens=True)

        keep_tokens = {'<summary>', '</summary>', '<table>', '</table>'}
        new_prompts_text_list = self.custom_batch_decode(data.batch["prompts"], keep_special_tokens=keep_tokens)
        new_response_text_list = self.custom_batch_decode(data.batch["responses"], keep_special_tokens=keep_tokens)

        scores = []
        batch_size = len(prompts_text_list)

        for i, (prompt_id, prompt_txt, response) in enumerate(zip(
            data.non_tensor_batch["id"],
            prompts_text_list,
            response_text_list
        )):
            # # for debugging
            # response = "<think>这两款手机在定位和配置上差异明显。从用户对美食相关内容的关注来看，可能比较注重实用性和日常使用体验。\n\n第一款华为Hi畅享70Plus采用鸿蒙系统，主摄像头达到1.08亿像素，在拍照清晰度上有优势，适合记录生活瞬间。5000mAh电池容量加上40W有线快充，能够满足日常重度使用需求。侧边指纹识别设计便于单手操作，6.7英寸LCD屏幕在护眼方面表现不错。券后价1488元，在同配置产品中性价比合理。\n\n第二款华awPur 70 Pro+在硬件配置上更为突出，搭载骁龙8 Gen3处理器，性能表现更强。7800mAh超大电池容量是其最大亮点，续航能力非常出色，适合长时间使用。6.9英寸OLED屏幕配合120Hz刷新率，显示效果更加流畅细腻。3200万像素前置摄像头在自拍方面有明显优势。券后价888元，价格相当实惠。\n\n从实用角度考量，如果更看重系统体验和拍照效果，华为Hi畅享70Plus的鸿蒙系统和高像素主摄比较合适。如果关注性能和续航表现，华awPur 70 Pro+的大电池和高性能处理器更有吸引力。两款产品价格相差600元，可根据个人预算和使用需求来选择。</think><summary>\n## 选购建议\n追求**鸿蒙系统体验+高像素主摄**：首选**【华为智选】Hi 畅享70Plus**\n- **核心优势**：HarmonyOS操作系统流畅稳定，1.08亿像素主摄像头适合拍摄地方特色小吃和日常美食记录，5000mAh电池支持全天候使用。\n- **价格优势**：券后价1488元。\n\n注重**大电池续航+高性价比**：选**2025新款Huaw/华 Pur 70 Pro+**\n- **核心优势**：7800mAh超大电池提供超长续航，适合夏季户外或长时间使用场景，120Hz刷新率屏幕提升视觉流畅度。\n- **价格优势**：券后价888元，比前者便宜600元。\n</summary>\n\n<table>\n## 核心差异对比\n| 对比维度 | 【华为智选】Hi 畅享70Plus | 2025新款Huaw/华 Pur 70 Pro+ |\n| :------- | :----------------------- | :-------------------------- |\n| 操作系统 | HarmonyOS | Android |\n| 电池容量 | 5000mAh | **7800mAh** |\n| 主摄像头像素 | **1.08亿像素** | 6400万像素 |\n| 前置摄像头像素 | 800万像素 | **3200万像素** |\n| 屏幕刷新率 | -- | **120Hz** |\n| CPU型号 | 高通骁龙™ 680 | **骁龙8 Gen3** |\n</table>"
            # user_history = {"gender": "男", "se_keyword_list": ["地方特色小吃特产", "山东土特产大全", "70后80后怀旧零食", "早餐大全各种美食", "适合早上吃的早餐"], "clk_itm_list": ["山东潍坊特产手工麦香千层火烧杠子头硬面馍老面烧饼馒头面食烙馍", "山东特产千层麦香火烧纯手工老面馒头硬面杠子头馍馍面食早餐烧饼", "山东特产千层麦香火烧手工老面馒头纯硬面杠子头馍馍面食早餐烧饼", "陕西西安特产白吉馍饼早餐速食即食半成品家用肉夹馍老面商用批发", "山东特产潍坊千层火烧手工杠子头低脂面食硬面麦香烧饼早餐馒头"], "pay_itm_list": ["标题:正宗山东小颗粒白沙花生米新货可油炸无壳花生米批发特级, 类目:花生, sku:食品口味:5斤大颗粒（净重2480g-2510g）, 价格:29.89", "标题:贵州特产老干妈风味豆豉油辣椒酱280g香辣酱下饭菜酱下拌饭酱, 类目:酱类调料, sku:口味:老干妈风味豆豉【2瓶】, 价格:18.54", "标题:夏季新款潮流男士短袖T恤衫冰丝透气百搭圆领体恤上衣速干运动衣, 类目:T恤, sku:颜色:黑色+黑色【两件装】,尺码:xl 120-135斤, 价格:10.8", "标题:山东特产潍坊全麦千层火烧纯手工杠子头面食硬面麦香烧饼馒头, 类目:包点, sku:口味:特价抢：品质保证【10一大袋】最后20份, 价格:9.1", "标题:新货特价贵州特产老干妈风味豆豉280辣椒酱豆豉辣椒拌面下饭菜, 类目:辣椒酱, sku:口味:280g风味豆豉2瓶, 价格:18.47"], "cart_itm_list": ["标题:贵州特产老干妈风味豆豉油辣椒酱280g香辣酱下饭菜酱下拌饭酱, 类目:酱类调料, sku:口味:老干妈风味豆豉【2瓶】, 价格:18.54"], "collect_itm_list": ["黄河滩咸鸭蛋流油鸭蛋新鲜流油咸鸭蛋特产散养鸭蛋早餐配"]}
            # product_info = {}
            # product_info['商品1信息'] = "{\"标题\": \"【华为智选】Hi 畅享70Plus 新款5G手机鸿蒙智能AI华为智选手机官方旗舰正品店官网补贴畅享70X/70pro/70S\", \"券后价\": \"1488.00\", \"售后服务\": \"该商品具有的售后服务如下：\\n  名称：极速退款, 描述：满足相应条件时，信誉良好的用户在退货寄出后，享受极速退款到账。\\n  名称：退货宝, 描述：退货运费险保障：选择上门取件，自动减免首重运费；若选择自寄，参照首重标准补偿，具体以“订单详情-退货宝”为准\\n  名称：7天无理由退换, 描述：满足相应条件（激活或使用后不支持）时，消费者可申请 “7天无理由退换货”\\n  名称：假一赔四, 描述：若消费者收到商品非“正品”，商家需额外向消费者支付商品实际成交金额的四倍作为赔偿\", \"评价观点\": \"\", \"物流信息\": null, \"屏幕材质\": \"LCD\", \"指纹识别方式\": \"侧边指纹\", \"运行内存\": \"12GB\", \"是否支持NFC\": \"不支持NFC\", \"前置摄像头像素\": \"800万像素\", \"操作系统\": \"HarmonyOS\", \"屏幕尺寸\": \"6.7英寸\", \"是否支持无线充电\": \"否\", \"品牌\": \"Huawei/华为\", \"有线充电功率\": \"40W\", \"主摄像头像素\": \"1.08亿像素\", \"屏幕分辨率\": \"2388x1080\", \"电池容量\": \"5000mAh\", \"颜色分类\": \"冰晶蓝\", \"CPU品牌\": \"高通\", \"网络类型\": \"SA/NSA双模(5G)\", \"CPU型号\": \"高通骁龙™ 680\"}"
            # product_info['商品2信息'] = "{\"标题\": \"2025新款Huaw/华 Pur 70 Pro+正品大屏鸿蒙No/va13官方旗舰5G手机\", \"券后价\": \"888.00\", \"售后服务\": \"该商品具有的售后服务如下：\\n  名称：退货宝, 描述：退货运费险保障：选择上门取件，自动减免首重运费；若选择自寄，参照首重标准补偿，具体以“订单详情-退货宝”为准\\n  名称：7天无理由退货, 描述：满足相应条件（激活或使用后不支持）时，消费者可申请 “7天无理由退货”\\n  名称：极速退款, 描述：满足相应条件时，信誉良好的用户在退货寄出后，享受极速退款到账。\", \"评价观点\": \"\", \"物流信息\": null, \"无线充电功率\": \"40W\", \"运行内存\": \"16GB\", \"是否支持无线充电\": \"否\", \"屏幕刷新率\": \"120Hz\", \"电池容量\": \"7800mAh\", \"操作系统\": \"Android/安卓\", \"屏幕尺寸\": \"6.9英寸\", \"是否支持NFC\": \"不支持NFC\", \"品牌\": \"HY\", \"CPU型号\": \"骁龙8 Gen3\", \"屏幕分辨率\": \"2560x1536\", \"前置摄像头像素\": \"3200万像素\", \"CPU品牌\": \"高通\", \"屏幕材质\": \"OLED\", \"颜色分类\": \"钛黑色\", \"主摄像头像素\": \"6400万像素\", \"网络类型\": \"5G\"}"
            # #
            breakpoint()
            # 从response中提取reasoning_content和answer_content
            is_valid_format, reasoning_content, answer_content = self._extract_answer_content(response)

            # 如果基本格式不正确(无法从提取出正确的cot和ans)，直接给低分
            if not is_valid_format:
                final_score = -2.0
                info = {
                    "prompt_id": prompt_id,
                    "score": final_score,
                    "task_description": prompt_txt,
                    "action_sequence": response,
                    "format_check": "跳过格式检查",
                    "format_score": 0.0,
                    "hallucination_check": "跳过幻觉检查",
                    "hallucination_score": 0.0
                }
                scores.append(final_score)
                self.logger.info(f"{json.dumps(info, ensure_ascii=False)}")
                continue

            # 组织商品信息
            product_info = {}
            for j in range(1, 4):  # 商品1、2、3
                key = f'商品{j}信息'
                if key in data.non_tensor_batch:
                    product_list = data.non_tensor_batch[key]
                    if i < len(product_list) and product_list[i]:
                        product_info[key] = product_list[i]

            # 获取用户历史
            user_history_list = data.non_tensor_batch.get("user_history", [None] * batch_size)
            user_history = user_history_list[i] if i < len(user_history_list) else None
            
            # 检查格式
            format_score, format_msg = self._check_format(answer_content, product_info)
            
            # 检查幻觉
            hallucination_score, hallucination_msg = self._check_hallucination(
                answer_content, product_info, user_history
            )
            
            # 检查内容质量
            ans_quality_score, ans_quality_msg = self._check_ans_quality(
                answer_content, product_info, user_history
            )

            # 综合计算最终分数
            base_score = format_score * hallucination_score
            final_score = base_score * 0.8 + ans_quality_score * 0.2

            info = {
                "prompt_id": prompt_id,
                "score": final_score,
                "task_description": prompt_txt,
                "action_sequence": response,
                "format_check": format_msg,
                "format_score": format_score,
                "hallucination_check": hallucination_msg,
                "hallucination_score": hallucination_score
            }
            scores.append(final_score)

            self.logger.info(f"{json.dumps(info, ensure_ascii=False)}")

        scores_tensor = torch.tensor(scores, dtype=torch.float16)
        token_level_rewards = torch.zeros_like(data.batch["responses"], dtype=torch.float16)
        response_level_rewards = scores_tensor

        custom_metrics = {}
        output = DataProto.from_dict(
            tensors={
                "token_level_rewards": token_level_rewards,
                "response_level_rewards": response_level_rewards,
                "scores": scores_tensor,
            },
            meta_info={
                "metrics": metrics,
                "custom_metrics": custom_metrics
            }
        )

        print(f"Computed rewards for {len(scores)} samples")
        # breakpoint()
        return output