from typing import Optional, Union, Dict, List, Any, Tuple
import json
import re
import torch
import requests
import time
import traceback
import numpy as np
from functools import partial
import tensordict
from tensordict import TensorDict
from roll.configs.worker_config import WorkerConfig
from roll.distributed.executor.worker import Worker
from roll.distributed.scheduler.decorator import Dispatch, register
from roll.distributed.scheduler.protocol import DataProto
from roll.distributed.strategy.factory import create_strategy
from roll.distributed.strategy.strategy import InferenceStrategy, TrainStrategy
from roll.models.model_providers import default_tokenizer_provider, default_reward_model_provider
from roll.utils.logging import get_logger
from roll.utils.context_managers import state_offload_manger
from roll.utils.prompt import *
from roll.datasets.chat_template import get_chat_template
import ray


@ray.remote
class ShoppingCartLLMJudgeRewardWorker(Worker):
    """
    Reward Worker that uses LLM-as-judge to compute rewards for shopping cart tasks.
    """

    def __init__(self, worker_config: WorkerConfig):
        super().__init__(worker_config=worker_config)
        self.rank_info.dp_rank = self.rank_info.rank
        self.rank_info.dp_size = self.rank_info.world_size
        self.tokenizer = None
        self.strategy: Optional[Union[InferenceStrategy, TrainStrategy]] = None

        # Shopping cart LLM judge相关配置
        self.judge_prompt = self.worker_config.judge_prompt if hasattr(self.worker_config, "judge_prompt") else None
        self.judge_model_type = (
            self.worker_config.judge_model_type if hasattr(self.worker_config, "judge_model_type") else "api"
        )
        self.judge_model_name = (
            self.worker_config.judge_model_name if hasattr(self.worker_config, "judge_model_name") else "claude_opus4"
        )

        # 初始化OpenAI客户端
        self.client = None

    @register(dispatch_mode=Dispatch.ONE_TO_ALL)
    def initialize(self, pipeline_config):
        super().initialize(pipeline_config)
        if self.judge_model_type == "api":
            from openai import OpenAI
            self.client = OpenAI(
                api_key='27db1fc058c1861870be4c21a7f93cdc',
                base_url="https://idealab.alibaba-inc.com/api/openai/v1",
            )
            self.tokenizer = default_tokenizer_provider(model_args=self.worker_config.model_args)
            print(f"{self.worker_name} initialized with API model")

        elif self.judge_model_type == "inference":
            self.strategy = create_strategy(worker=self)
            self.strategy.initialize(model_provider=default_reward_model_provider)
            self.tokenizer = self.strategy.tokenizer
            print(f"{self.worker_name} initialized with inference model")
            self.strategy.offload_states()
        else:
            raise ValueError(f"Unsupported model type: {self.judge_model_type}")

    def _call_api_model(self, messages: List[Dict], retry_times=3) -> str:
        output = ""
        if not self.client:
            raise ValueError("OpenAI client not initialized")
        
        while retry_times > 0:
            retry_times -= 1
            try:
                completion = self.client.chat.completions.create(
                    model="claude_opus4",
                    messages=messages,
                    temperature=0.1,
                )
                
                output = completion.choices[0].message.content
                
                if output is not None and output != "":
                    break
            except Exception as e:
                print(f"API call error: {e}")
                continue
        
        self.logger.info(f"shopping cart judge model api output: {str(output)}")
        return output

    def _run_local_inference(self, messages: Dict) -> str:
        if not self.strategy:
            raise ValueError("Strategy not initialized for local inference")

        template_name = self.worker_config.data_args.template
        chat_template_func = get_chat_template(template_name, self.tokenizer)
        text = chat_template_func(messages)

        tokenized = self.tokenizer(text, return_tensors="pt")
        input_ids = tokenized["input_ids"].to("cuda")
        attention_mask = tokenized["attention_mask"].to("cuda")

        generation_config = self.worker_config.generating_args.to_dict()
        generation_config["eos_token_id"] = [self.tokenizer.eos_token_id]
        generation_config["pad_token_id"] = self.tokenizer.pad_token_id

        data = DataProto(
            batch=TensorDict({"input_ids": input_ids, "attention_mask": attention_mask}, batch_size=input_ids.shape[0])
        )
        data = data.to("cuda")
        data.meta_info = {"micro_batch_size": self.worker_config.infer_batch_size}

        with torch.no_grad():
            output = self.strategy.generate(batch=data, generation_config=generation_config)
            if isinstance(output, torch.Tensor):
                generate_ids = output[:, len(input_ids[0]) :]
            else:
                generate_ids = output.batch["input_ids"][:, len(input_ids[0]) :]

        output = self.tokenizer.decode(generate_ids[0], skip_special_tokens=True)
        self.logger.info(f"shopping cart judge model inference output: {str(output)}")
        return output.strip()

    def _check_hallucination(self, answer_content: str, product_info: Dict = None, user_history: Dict = None) -> Tuple[bool, str]:
        """
        检查模型输出是否存在幻觉
        
        :param answer_content: 模型的回答内容
        :param product_info: 商品信息
        :param user_history: 用户历史行为
        :return: (float, str) - 幻觉评分(0-1)，检查结果描述
        """
        try:
            system_prompt = """你是一位专业的AI输出质量检测专家，需要判断模型输出是否存在幻觉。
幻觉定义：模型输出中包含输入信息中不存在的内容，且无法从输入信息中合理推导得出。

请仔细分析商品信息和用户历史行为，判断模型输出属于以下哪种情况：
- 输入信息包括商品参数与商品用户评价
1. 【严重幻觉】模型输出包含输入信息中不存在的具体内容
2. 【内容矛盾】模型输出与输入信息明确矛盾或冲突
3. 【无幻觉】模型输出完全基于输入信息或合理推导

【重要】以下情况不视为幻觉：
- 基于输入信息的合理推导和估算
- 对商品属性的主观描述和评价
- 基于用户历史行为的合理推断
- 来自商品用户评价的内容

请只回答以下三种情况之一：
- "严重幻觉"
- "内容矛盾" 
- "无幻觉"

并简要说明判断理由。"""

            # 构建用户提示
            user_prompt = "请判断以下模型输出是否存在幻觉：\n\n"
            
            # 添加商品信息
            if product_info:
                user_prompt += "【输入商品信息】\n"
                user_prompt += json.dumps(product_info, ensure_ascii=False) + "\n\n"
            
            # 添加用户历史行为
            if user_history:
                user_prompt += "【用户历史行为】\n"
                user_prompt += json.dumps(user_history, ensure_ascii=False) + "\n\n"
            
            # 添加模型回答内容
            user_prompt += "【模型回答内容】\n"
            user_prompt += answer_content + "\n\n"
            
            user_prompt += "请判断上述模型回答内容属于哪种情况，只需回答'严重幻觉'、'内容矛盾'或'无幻觉'，并简要说明理由。"
            
            # 调用API检查幻觉
            if self.judge_model_type == "api":
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
                llm_response = self._call_api_model(messages)
                breakpoint()
            else:
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
                llm_response = self._run_local_inference(messages)
            
            # 解析LLM响应并分配分数
            if "无幻觉" in llm_response:
                score = 1.0
                return score, f"无幻觉: {llm_response}"
            elif "内容矛盾" in llm_response:
                score = 0.3
                return score, f"内容矛盾: {llm_response}"
            elif "严重幻觉" in llm_response:
                score = 0.0
                return score, f"严重幻觉: {llm_response}"
            else:
                # 默认情况，无法明确判断
                score = 0.5
                return score, f"无法明确判断: {llm_response}"
                
        except Exception as e:
            self.logger.error(f"检查幻觉时出错: {e}")
            return 0.0, f"幻觉检查失败: {str(e)}"

    def _check_format(self, answer_content: str, product_info: Dict = None) -> Tuple[float, str]:
        """
        检查answer_content的格式是否符合要求
        
        :param answer_content: 模型的回答内容
        :param product_info: 商品信息字典
        :return: (float, str) - 格式评分(0-1)，检查结果描述
        """
        try:
            # (1) 检查是否只有一个summary和一个table
            summary_count = answer_content.count('<summary>')
            summary_end_count = answer_content.count('</summary>')
            table_count = answer_content.count('<table>')
            table_end_count = answer_content.count('</table>')
            
            if summary_count != 1 or summary_end_count != 1:
                return 0.0, f"summary标签数量错误: <summary>={summary_count}, </summary>={summary_end_count}"
            
            if table_count != 1 or table_end_count != 1:
                return 0.0, f"table标签数量错误: <table>={table_count}, </table>={table_end_count}"
            
            # 检查基本格式：<summary>...</summary><table>...</table>
            pattern = r"^<summary>.*?</summary><table>.*?</table>$"
            if not re.match(pattern, answer_content.strip(), re.DOTALL):
                return 0.0, "基本格式错误: 不符合<summary>...</summary><table>...</table>格式"
            
            # (2) 检查商品数量和表格列数是否一致
            if product_info:
                expected_product_count = len([k for k in product_info.keys() if k.startswith('商品') and k.endswith('信息')])
                
                # 提取表格内容
                table_match = re.search(r'<table>\s*(.*?)\s*</table>', answer_content, re.DOTALL)
                if not table_match:
                    return 0.0, "无法提取表格内容"
                
                table_content = table_match.group(1).strip()
                
                # 查找表头行
                lines = table_content.split('\n')
                header_line = None
                for line in lines:
                    line = line.strip()
                    if line.startswith('| 对比维度'):
                        header_line = line
                        break
                
                if not header_line:
                    return 0.0, "表格缺少'对比维度'表头"
                
                # 计算表格列数
                actual_columns = header_line.count('|') - 1
                expected_columns = expected_product_count + 1  # 商品数量 + 对比维度列
                
                if actual_columns != expected_columns:
                    return 0.0, f"表格列数不匹配: 期望{expected_columns}列，实际{actual_columns}列"
                
                # (3) 检查商品顺序是否一致
                order_check_result = self._check_product_order(answer_content, product_info)
                if not order_check_result[0]:
                    return 0.0, f"商品顺序错误: {order_check_result[1]}"
                
                # (4) 检查是否有重复或全空的对比维度
                duplicate_check_result = self._check_duplicate_dimensions(table_content, product_info)
                if not duplicate_check_result[0]:
                    return 0.0, f"表格内容错误: {duplicate_check_result[1]}"
            
            return 1.0, "格式完全正确"
                    
        except Exception as e:
            self.logger.error(f"格式检查时出错: {e}")
            return 0.0, f"格式检查失败: {str(e)}"

    def _check_product_order(self, answer_content: str, product_info: Dict) -> Tuple[bool, str]:
        """检查表格中商品顺序是否与输入数据一致"""
        try:
            # 提取商品标题
            product_titles = []
            for i in range(1, 4):
                key = f'商品{i}信息'
                if key in product_info:
                    try:
                        info = json.loads(product_info[key]) if isinstance(product_info[key], str) else product_info[key]
                        title = info.get('标题', '') or info.get('name', '')
                        if title:
                            product_titles.append(title)
                    except:
                        continue
            
            # 提取表格中的商品名称
            table_match = re.search(r'<table>\s*(.*?)\s*</table>', answer_content, re.DOTALL)
            if not table_match:
                return False, "无法提取表格内容"
            
            table_content = table_match.group(1).strip()
            lines = table_content.split('\n')
            
            header_line = None
            for line in lines:
                line = line.strip()
                if line.startswith('| 对比维度'):
                    header_line = line
                    break
            
            if not header_line:
                return False, "找不到表头行"
            
            # 解析表头，提取商品名称
            columns = [col.strip() for col in header_line.split('|')]
            table_names = [col for col in columns[1:] if col and col != '对比维度']
            
            if len(product_titles) != len(table_names):
                return False, f"商品数量不匹配: 输入{len(product_titles)}个，表格{len(table_names)}个"
            
            # 检查顺序一致性（使用简单的包含关系检查）
            for i, (title, table_name) in enumerate(zip(product_titles, table_names)):
                # 简化的匹配逻辑：检查表格名称是否包含在商品标题中
                if not any(word in title for word in table_name.split() if len(word) > 1):
                    return False, f"商品{i+1}顺序不匹配: '{title[:20]}...' vs '{table_name}'"
            
            return True, "商品顺序正确"
            
        except Exception as e:
            return False, f"检查商品顺序时出错: {str(e)}"

    def _check_duplicate_dimensions(self, table_content: str, product_info: Dict) -> Tuple[bool, str]:
        """检查表格中是否有重复或全空的对比维度"""
        try:
            lines = table_content.split('\n')
            
            # 找到数据行（跳过表头和分隔行）
            data_rows = []
            found_separator = False
            for line in lines:
                line = line.strip()
                if line.startswith('| 对比维度'):
                    continue
                elif line.startswith('| :') or line.startswith('|:'):
                    found_separator = True
                    continue
                elif found_separator and line.startswith('|'):
                    data_rows.append(line)
            
            if not data_rows:
                return True, "表格无数据行"
            
            # 检查每一行的商品列是否都相同或都为空
            invalid_rows = []
            for row in data_rows:
                columns = [col.strip() for col in row.split('|')[1:-1]]
                if len(columns) < 2:
                    continue
                
                # 获取商品列（跳过第一列的维度名称）
                product_columns = columns[1:]
                
                # 检查是否都为空（包括--表示的空值）
                is_all_empty = all(not col or col == '--' or re.sub(r'[\*\-\s]', '', col) == '' for col in product_columns)
                
                # 检查是否完全相同
                is_all_same = len(set(product_columns)) == 1
                
                if is_all_empty or is_all_same:
                    dimension_name = columns[0] if columns else "未知维度"
                    invalid_rows.append(dimension_name)
            
            if invalid_rows:
                return False, f"存在重复或全空的对比维度: {', '.join(invalid_rows)}"
            
            return True, "对比维度检查通过"
            
        except Exception as e:
            return False, f"检查对比维度时出错: {str(e)}"

    def _extract_answer_content(self, response_text: str) -> tuple:
        """
        从actor模型的response中提取reasoning_content和answer_content部分
        首先检查是否符合基本格式要求："<think>...</think><summary>...</summary><table>...</table>"
        如果找不到<summary>标签，则寻找"### 选购建议"作为备选方案

        :param response_text: actor模型的完整输出
        :return: (is_valid_format, reasoning_content, answer_content)
                is_valid_format: 是否符合基本格式
                reasoning_content: <think>...</think>内容
                answer_content: <summary>...</summary><table>...</table>内容 或 ### 选购建议...内容
        """
        try:
            response_text = response_text.strip()

            # 首先尝试标准格式：<think>...</think><summary>...</summary><table>...</table>
            required_tags = ['<think>', '</think>', '<summary>', '</summary>', '<table>', '</table>']
            has_all_tags = all(tag in response_text for tag in required_tags)

            if has_all_tags:
                # 检查标签数量是否正确（每个标签应该只出现一次）
                tags_count_correct = all(response_text.count(tag) == 1 for tag in required_tags)

                if tags_count_correct:
                    # 检查标签顺序是否正确
                    think_start = response_text.find('<think>')
                    think_end = response_text.find('</think>')
                    summary_start = response_text.find('<summary>')
                    summary_end = response_text.find('</summary>')
                    table_start = response_text.find('<table>')
                    table_end = response_text.find('</table>')

                    # 验证标签顺序
                    if think_start < think_end < summary_start < summary_end < table_start < table_end:
                        # 提取reasoning_content（<think>...</think>内容）
                        reasoning_content = response_text[think_start + 7:think_end].strip()  # 7 = len('<think>')

                        # 提取answer_content（<summary>...</summary><table>...</table>内容）
                        answer_content = response_text[summary_start:table_end + 8].strip()  # 8 = len('</table>')

                        # 检查内容是否为空
                        if reasoning_content and answer_content:
                            return True, reasoning_content, answer_content

            # 备选方案：寻找<think>...</think>和### 选购建议
            if '<think>' in response_text and '</think>' in response_text and '### 选购建议' in response_text:
                think_start = response_text.find('<think>')
                think_end = response_text.find('</think>')
                advice_start = response_text.find('### 选购建议')

                # 验证顺序：<think>应该在</think>之前，</think>应该在### 选购建议之前
                if think_start < think_end < advice_start:
                    # 提取reasoning_content（<think>...</think>内容）
                    reasoning_content = response_text[think_start + 7:think_end].strip()  # 7 = len('<think>')

                    # 提取answer_content（### 选购建议...到末尾）
                    answer_content = response_text[advice_start:].strip()

                    # 检查内容是否为空
                    if reasoning_content and answer_content:
                        self.logger.info("使用备选格式：<think>...</think> + ### 选购建议...")
                        return True, reasoning_content, answer_content

            # 如果都不符合，返回失败
            self.logger.warning("Response不符合任何已知格式")
            return False, "", ""

        except Exception as e:
            self.logger.warning(f"提取内容失败: {e}")
            return False, "", ""

    @register(dispatch_mode=Dispatch.DP_MP_COMPUTE, clear_cache=False)
    def compute_rewards(self, data: DataProto):
        global_step = data.meta_info.get("global_step", 0)
        is_offload_states = data.meta_info.get("is_offload_states", True)
        metrics = {}

        if self.judge_model_type == "inference" and self.strategy:
            with state_offload_manger(
                strategy=self.strategy,
                metrics=metrics,
                metric_infix=f"{self.cluster_name}/compute_rewards",
                is_offload_states=is_offload_states,
            ):
                return self._compute_rewards_impl(data, metrics)
        else:
            return self._compute_rewards_impl(data, metrics)

    def _compute_rewards_impl(self, data: DataProto, metrics: Dict):
        """计算购物车任务的奖励，包含基本格式检查、格式检查和幻觉检测"""
        prompts_text_list = self.tokenizer.batch_decode(data.batch["prompts"], skip_special_tokens=True)
        response_text_list = self.tokenizer.batch_decode(data.batch["responses"], skip_special_tokens=True)

        scores = []
        batch_size = len(prompts_text_list)

        for i, (prompt_id, prompt_txt, response) in enumerate(zip(
            data.non_tensor_batch["id"],
            prompts_text_list,
            response_text_list
        )):
            # 从response中提取reasoning_content和answer_content
            is_valid_format, reasoning_content, answer_content = self._extract_answer_content(response)

            # 如果基本格式不正确，直接给低分
            if not is_valid_format:
                final_score = 0.0
                info = {
                    "prompt_id": prompt_id,
                    "score": final_score,
                    "task_description": prompt_txt,
                    "action_sequence": response,
                    "reasoning_content": "",
                    "answer_content": "",
                    "basic_format_check": "基本格式不正确，不符合<think>...</think><summary>...</summary><table>...</table>要求",
                    "format_check": "跳过格式检查",
                    "format_score": 0.0,
                    "hallucination_check": "跳过幻觉检查",
                    "hallucination_score": 0.0
                }
                scores.append(final_score)
                self.logger.info(f"{json.dumps(info, ensure_ascii=False)}")
                continue

            # 组织商品信息
            product_info = {}
            for j in range(1, 4):  # 商品1、2、3
                key = f'商品{j}信息'
                if key in data.non_tensor_batch:
                    product_list = data.non_tensor_batch[key]
                    if i < len(product_list) and product_list[i]:
                        product_info[key] = product_list[i]

            # 获取用户历史
            user_history_list = data.non_tensor_batch.get("user_history", [None] * batch_size)
            user_history = user_history_list[i] if i < len(user_history_list) else None

            # 检查格式
            format_score, format_msg = self._check_format(answer_content, product_info)

            # 检查幻觉
            hallucination_score, hallucination_msg = self._check_hallucination(
                answer_content, product_info, user_history
            )

            # 综合计算最终分数
            final_score = format_score * hallucination_score

            info = {
                "prompt_id": prompt_id,
                "score": final_score,
                "task_description": prompt_txt,
                "action_sequence": response,
                "reasoning_content": reasoning_content,
                "answer_content": answer_content,
                "basic_format_check": "基本格式正确",
                "format_check": format_msg,
                "format_score": format_score,
                "hallucination_check": hallucination_msg,
                "hallucination_score": hallucination_score
            }
            scores.append(final_score)

            self.logger.info(f"{json.dumps(info, ensure_ascii=False)}")

        scores_tensor = torch.tensor(scores, dtype=torch.float16)
        token_level_rewards = torch.zeros_like(data.batch["responses"], dtype=torch.float16)
        response_level_rewards = scores_tensor

        custom_metrics = {}
        output = DataProto.from_dict(
            tensors={
                "token_level_rewards": token_level_rewards,
                "response_level_rewards": response_level_rewards,
                "scores": scores_tensor,
            },
            meta_info={
                "metrics": metrics,
                "custom_metrics": custom_metrics
            }
        )

        print(f"Computed rewards for {len(scores)} samples")
        return output