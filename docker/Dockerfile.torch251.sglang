# NVIDIA official image (ubuntu-22.04 + cuda-12.4 + python-3.10)
# https://docs.nvidia.com/deeplearning/frameworks/pytorch-release-notes/rel-24-08.html
FROM nvcr.io/nvidia/pytorch:24.05-py3

ENV DEBIAN_FRONTEND=noninteractive
ENV PIP_ROOT_USER_ACTION=ignore

RUN pip install --upgrade pip setuptools wheel --trusted-host mirrors.aliyun.com --index-url https://mirrors.aliyun.com/pypi/simple/

RUN pip uninstall -y torch torchvision torch-tensorrt \
    flash-attn transformer-engine \
    cudf dask-cuda cugraph cugraph-service-server cuml raft-dask cugraph-dgl cugraph-pyg dask-cudf

RUN pip install torch==2.5.1 torchvision==0.20.1 torchaudio==2.5.1 --index-url https://download.pytorch.org/whl/cu124

RUN pip uninstall -y opencv opencv-python opencv-python-headless && \
    rm -rf /usr/local/lib/python3.10/dist-packages/cv2/ && \
    pip install opencv-python-headless==********* --trusted-host mirrors.aliyun.com --index-url https://mirrors.aliyun.com/pypi/simple/

RUN pip install "numpy==1.26.4" "spacy==3.7.5" "weasel==0.4.1" \
    transformer-engine[pytorch]==1.12.0 megatron-core==0.11.0 deepspeed==0.16.0 \
    --trusted-host mirrors.aliyun.com --index-url https://mirrors.aliyun.com/pypi/simple/

RUN pip install flash_attn==2.6.3 sglang[srt,torch_memory_saver]==0.4.3.post4 \
    --trusted-host mirrors.aliyun.com --index-url https://mirrors.aliyun.com/pypi/simple/

WORKDIR /build
COPY . .

ARG apex_url=git+https://github.com/NVIDIA/apex.git@25.04
RUN pip uninstall -y apex && \
    MAX_JOBS=32 NINJA_FLAGS="-j32" NVCC_APPEND_FLAGS="--threads 32" \
    pip install -v --disable-pip-version-check --no-cache-dir --no-build-isolation \
    --config-settings "--build-option=--cpp_ext --cuda_ext --parallel 32" ${apex_url}

RUN rm -rf /build
WORKDIR /workspace

RUN cp /etc/apt/sources.list /etc/apt/sources.list.bak && \
    { \
    echo "deb https://mirrors.aliyun.com/ubuntu/ jammy main restricted universe multiverse"; \
    echo "deb https://mirrors.aliyun.com/ubuntu/ jammy-security main restricted universe multiverse"; \
    echo "deb https://mirrors.aliyun.com/ubuntu/ jammy-updates main restricted universe multiverse"; \
    echo "deb https://mirrors.aliyun.com/ubuntu/ jammy-backports main restricted universe multiverse"; \
    } > /etc/apt/sources.list

RUN apt-get update && apt-get install -y zip

RUN apt-get update && apt-get install -y openjdk-21-jdk
ENV JAVA_HOME /usr/lib/jvm/java-21-openjdk-amd64
