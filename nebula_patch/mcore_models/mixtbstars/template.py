import re

from mcore_adapter.models.converter.template import (Template, 
    register_template,
    RenameConverOp,
    StackConverOp,
    QKVConverOp,
    QKVBiasConverOp
    )


class MixTBStarsTemplate(Template):
    def convert_hf_to_mca_config_kws(self, hf_config, **kw_args):
        first_k_dense_layers = getattr(hf_config, "first_k_dense_layers", None)
        if first_k_dense_layers:
            kw_args["moe_layer_freq"] = [0] * first_k_dense_layers + [1] * (
                hf_config.num_hidden_layers - first_k_dense_layers
            )
        shared_ffn_size = hf_config.num_shared_experts * hf_config.intermediate_size
        if shared_ffn_size == 0:
            kw_args["moe_shared_expert_intermediate_size"] = None
            kw_args["ffn_hidden_size"] = hf_config.intermediate_size
        else:  # the ffn_hidden_size of first_k_dense_layers is the same as moe_shared_expert_intermediate_size
            kw_args["moe_shared_expert_intermediate_size"] = shared_ffn_size
            kw_args["ffn_hidden_size"] = shared_ffn_size
        return super().convert_hf_to_mca_config_kws(hf_config, **kw_args)

    def convert_mca_to_hf_config(self, mca_config, **kw_args):
        if isinstance(mca_config.moe_layer_freq, list):
            kw_args["first_k_dense_layers"] = mca_config.moe_layer_freq.count(0)
        if mca_config.moe_shared_expert_intermediate_size:
            kw_args["num_shared_experts"] = mca_config.moe_shared_expert_intermediate_size // mca_config.moe_ffn_hidden_size
        else:
            kw_args["num_shared_experts"] = 0
        return super().convert_mca_to_hf_config(mca_config, **kw_args)

    def add_hf_weight(self, name, weight):
        name2weights = super().add_hf_weight(name, weight)
        if name2weights is None:
            return None
        first_k_dense_layers = 0
        if isinstance(self.mca_config.moe_layer_freq, list):
            first_k_dense_layers = self.mca_config.moe_layer_freq.count(0)
        if first_k_dense_layers <= 0:
            return name2weights
        res = {}
        pattern = r"^decoder\.layers\.([^\.]+)\.(mlp\.shared_experts|pre_mlp_layernorm)\..+$"
        for name, weight in name2weights.items():
            match = re.match(pattern, name)
            if match and int(match.group(1)) < first_k_dense_layers:
                name = name.replace("shared_experts.", "").replace("pre_mlp_layernorm.", "mlp.linear_fc1.layer_norm_")
            res[name] = weight
        return res

    def add_mca_weight(self, name, weight):
        first_k_dense_layers = 0
        if isinstance(self.mca_config.moe_layer_freq, list):
            first_k_dense_layers = self.mca_config.moe_layer_freq.count(0)
        if first_k_dense_layers <= 0:
            return super().add_mca_weight(name, weight)
        pattern = r"^decoder\.layers\.([^\.]+)\.mlp\..+$"
        match = re.match(pattern, name)
        if match and int(match.group(1)) < first_k_dense_layers:
            if "layer_norm_" in name:
                name = name.replace("mlp.linear_fc1.layer_norm_", "pre_mlp_layernorm.")
            else:
                name = name.replace("mlp.", "mlp.shared_experts.")
        return super().add_mca_weight(name, weight)
