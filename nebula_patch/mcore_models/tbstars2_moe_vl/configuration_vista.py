# coding=utf-8
# Copyright 2025 The Taotian Future Lab Team and The HuggingFace Inc. team. All rights reserved.
#
# This code is based on EleutherAI's GPT-NeoX library and the GPT-NeoX
# and OPT implementations in this library. It has been modified from its
# original forms to accommodate minor architectural differences compared
# to GPT-NeoX and OPT used by the Meta AI team that trained the model.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""MixTBStarsVista configuration"""

import os
from typing import Any, Dict, Optional, Union

from transformers import WhisperConfig
from transformers.configuration_utils import PretrainedConfig
from transformers.utils import logging


logger = logging.get_logger(__name__)

TBSTARS_VISTA_PRETRAINED_CONFIG_ARCHIVE_MAP = {}


class TBStarsHybridVisionConfig(PretrainedConfig):
    model_type = "hybrid_vision_model"
    base_config_key = "vision_config"

    def __init__(
        self,
        depth=32,
        hidden_size=3584,
        hidden_act="silu",
        intermediate_size=3420,
        num_heads=16,
        in_channels=3,
        patch_size=14,
        spatial_merge_size=2,
        temporal_patch_size=2,
        tokens_per_second=4,
        window_size=112,
        out_hidden_size=3584,
        fullatt_block_indexes=[7, 15, 23, 31],
        **kwargs,
    ):
        super().__init__(**kwargs)

        self.depth = depth
        self.hidden_size = hidden_size
        self.hidden_act = hidden_act
        self.intermediate_size = intermediate_size
        self.num_heads = num_heads
        self.in_channels = in_channels
        self.patch_size = patch_size
        self.spatial_merge_size = spatial_merge_size
        self.temporal_patch_size = temporal_patch_size
        self.tokens_per_second = tokens_per_second
        self.window_size = window_size
        self.fullatt_block_indexes = fullatt_block_indexes
        self.out_hidden_size = out_hidden_size


class TBStarsSnippetPerceiverConfig(PretrainedConfig):
    model_type = "snippet_pack_mlp"

    def __init__(
        self,
        snippet_length=4,
        projector_name="mlp2x_gelu",
        feature_size=1024,
        hidden_size=1664,
        max_source_positions=1500,
        max_num_segments=20,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.snippet_length = snippet_length
        self.projector_name = projector_name
        self.feature_size = feature_size
        self.hidden_size = hidden_size
        self.max_source_positions = max_source_positions
        self.max_num_segments = max_num_segments


class TBStarsGridPerceiverConfig(PretrainedConfig):
    model_type = "grid_pack_mlp"

    def __init__(
        self,
        grid_size=2,
        feature_size=1024,
        hidden_size=1664,
        add_modality_start_end=True,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.grid_size = grid_size
        self.feature_size = feature_size
        self.hidden_size = hidden_size
        self.add_modality_start_end = add_modality_start_end


VISION_CONFIG_MAP = {"hybrid_vision_model": TBStarsHybridVisionConfig}

AUDIO_CONFIG_MAP = {
    "whisper": WhisperConfig,
}

PROJECTOR_CONFIG_MAP = {
    "snippet_pack_mlp": TBStarsSnippetPerceiverConfig,
    "grid_pack_mlp": TBStarsGridPerceiverConfig,
}


class MixTBStarsVistaConfig(PretrainedConfig):
    model_type = "tbstars2_moe_vista"
    keys_to_ignore_at_inference = ["past_key_values"]

    def __init__(
        self,
        vocab_size: int = 32000,
        hidden_size: int = 4096,
        intermediate_size: int = 11008,
        num_hidden_layers: int = 32,
        num_attention_heads: int = 32,
        num_key_value_heads: int = -1,
        hidden_act: str = "silu",
        max_position_embeddings: int = 2048,
        initializer_range: float = 0.02,
        rms_norm_eps: float = 1e-6,
        use_cache: bool = True,
        pad_token_id: int = 0,
        bos_token_id: int = 1,
        eos_token_id: int = 2,
        pretraining_tp: int = 1,
        tie_word_embeddings: bool = False,
        rope_theta: float = 10000.0,
        rope_scaling: Optional[Dict] = None,
        attention_bias: bool = False,
        attention_dropout: float = 0.0,
        hidden_dropout: float = 0.0,
        model_max_length: Optional[int] = None,
        ffn_multiple_of: Optional[int] = None,
        deep_norm: bool = False,
        vision_model_type: str = "hybrid_vision_model",
        vision_projector_type: str = "grid_pack_mlp",
        audio_model_type: str = "whisper",
        audio_projector_type: str = "snippet_pack_mlp",
        conversation_template: str = "chatml",
        image_aspect_ratio: str = "atom_grid",
        vision_config: Optional[Dict] = None,
        vision_projector_config: Optional[Dict] = None,
        audio_config: Optional[Dict] = None,
        audio_projector_config: Optional[Dict] = None,
        image_token_index: Optional[int] = None,
        audio_token_index: Optional[int] = None,
        num_experts_per_tok=2,
        num_routed_experts=8,
        num_shared_experts=0,
        router_aux_loss_coef=0.001,
        first_k_dense_layers=0,
        output_router_logits=True,
        **kwargs,
    ):
        assert hidden_size % num_attention_heads == 0
        assert intermediate_size is None or ffn_multiple_of is None
        if intermediate_size is None or intermediate_size <= 0:
            intermediate_size = ffn_multiple_of * (
                (int(8 / 3 * hidden_size) + ffn_multiple_of - 1) // ffn_multiple_of
            )
        if num_key_value_heads is None or num_key_value_heads <= 0:
            num_key_value_heads = num_attention_heads
        if model_max_length is None or model_max_length <= 0:
            model_max_length = max_position_embeddings

        # If `_config_dict` exist, we use them for the backward compatibility.
        # We pop out these attributes before calling `super().__init__` to avoid them being saved (which causes a lot
        # of confusion!).
        vision_config_dict = kwargs.pop("vision_config_dict", None)
        vision_projector_config_dict = kwargs.pop("vision_projector_config_dict", None)
        audio_config_dict = kwargs.pop("audio_config_dict", None)
        audio_projector_config_dict = kwargs.pop("audio_projector_config_dict", None)

        # Instead of simply assigning `[vision|vision_projector]_config_dict` to `[vision|vision_projector]_config`,
        # we use the values in `[vision|vision_projector]_config_dict` to update the values in
        # `[vision|vision_projector]_config`. The values should be same in most cases.
        _vision_config_dict = self._config_update(
            vision_config, vision_config_dict, vision_model_type, "vision"
        )
        _vision_projector_config_dict = self._config_update(
            vision_projector_config,
            vision_projector_config_dict,
            vision_projector_type,
            "vision_projector",
        )
        _audio_config_dict = self._config_update(
            audio_config, audio_config_dict, audio_model_type, "audio"
        )
        _audio_projector_config_dict = self._config_update(
            audio_projector_config,
            audio_projector_config_dict,
            audio_projector_type,
            "audio_projector",
        )

        self.vocab_size = vocab_size
        self.max_position_embeddings = max_position_embeddings
        self.hidden_size = hidden_size
        self.intermediate_size = intermediate_size
        self.num_hidden_layers = num_hidden_layers
        self.num_attention_heads = num_attention_heads
        self.num_key_value_heads = num_key_value_heads
        self.hidden_act = hidden_act
        self.initializer_range = initializer_range
        self.rms_norm_eps = rms_norm_eps
        self.pretraining_tp = pretraining_tp
        self.use_cache = use_cache
        self.rope_theta = rope_theta
        self.rope_scaling = rope_scaling
        self._rope_scaling_validation()
        self.attention_bias = attention_bias
        self.attention_dropout = attention_dropout
        self.hidden_dropout = hidden_dropout
        self.num_experts_per_tok = num_experts_per_tok
        self.num_routed_experts = num_routed_experts
        self.num_shared_experts = num_shared_experts
        self.router_aux_loss_coef = router_aux_loss_coef
        self.first_k_dense_layers = first_k_dense_layers
        self.output_router_logits = output_router_logits

        self.model_max_length = model_max_length
        self.ffn_multiple_of = ffn_multiple_of
        self.deep_norm = deep_norm
        self.conversation_template = conversation_template
        self.vision_config = (
            VISION_CONFIG_MAP[vision_model_type](**_vision_config_dict)
            if _vision_config_dict is not None
            else None
        )
        self.vision_projector_config = (
            PROJECTOR_CONFIG_MAP[vision_projector_type](**_vision_projector_config_dict)
            if _vision_projector_config_dict is not None
            else None
        )
        self.audio_config = (
            AUDIO_CONFIG_MAP[audio_model_type](**_audio_config_dict)
            if _audio_config_dict is not None
            else None
        )
        self.audio_projector_config = (
            PROJECTOR_CONFIG_MAP[audio_projector_type](**_audio_projector_config_dict)
            if _audio_projector_config_dict is not None
            else None
        )
        self.vision_model_type = (
            vision_model_type if self.vision_config is not None else None
        )
        self.vision_projector_type = (
            vision_projector_type if self.vision_projector_config is not None else None
        )
        self.audio_model_type = (
            audio_model_type if self.audio_config is not None else None
        )
        self.audio_projector_type = (
            audio_projector_type if self.audio_projector_config is not None else None
        )
        self.image_aspect_ratio = (
            image_aspect_ratio if self.vision_config is not None else None
        )
        self.image_token_index = (
            image_token_index if self.vision_config is not None else None
        )
        self.audio_token_index = (
            audio_token_index if self.audio_config is not None else None
        )

        super().__init__(
            pad_token_id=pad_token_id,
            bos_token_id=bos_token_id,
            eos_token_id=eos_token_id,
            tie_word_embeddings=tie_word_embeddings,
            **kwargs,
        )

    @staticmethod
    def _config_update(config, config_dict, config_class_name, module_type):
        if config_class_name is None:
            return None

        if module_type == "vision":
            assert config_class_name in VISION_CONFIG_MAP, NotImplementedError(
                f"Vision model type: {config_class_name}"
            )
            config_class = VISION_CONFIG_MAP[config_class_name]
        elif module_type == "audio":
            assert config_class_name in AUDIO_CONFIG_MAP, NotImplementedError(
                f"Audio model type: {config_class_name}"
            )
            config_class = AUDIO_CONFIG_MAP[config_class_name]
        elif module_type in ["vision_projector", "audio_projector"]:
            assert config_class_name in PROJECTOR_CONFIG_MAP, NotImplementedError(
                f"Projector type: {config_class_name}"
            )
            config_class = PROJECTOR_CONFIG_MAP[config_class_name]
        else:
            raise ValueError(f"Unknown module type {module_type}")

        if config_dict is not None:
            if config is None:
                config = {}

            # This is the complete result when using `projector_config_dict`.
            _config_dict = config_class(**config_dict).to_dict()

            if config_class.model_type == "clip_vision_model":
                # convert keys to string instead of integer
                if "id2label" in _config_dict:
                    _config_dict["id2label"] = {
                        str(key): value
                        for key, value in _config_dict["id2label"].items()
                    }

            # Give a warning if the values exist in both `_init_config_dict` and `text_config` but being different.
            for key, value in _config_dict.items():
                if (
                    key in config
                    and value != config[key]
                    and key not in ["transformers_version"]
                ):
                    # If specified in `init_config_dict`
                    if key in config_dict:
                        message = (
                            f"`{key}` is found in both `{module_type}_config_dict` and `{module_type}_config` but "
                            f'with different values. The value `{module_type}_config_dict["{key}"]` will be used instead.'
                        )
                    # If inferred from default argument values (just to be super careful)
                    else:
                        message = (
                            f"`{module_type}_config_dict` is provided which will be used to initialize "
                            f'`{config_class.model_type}`. The value `{module_type}_config["{key}"]` will be overriden.'
                        )
                    logger.info(message)

            # Update all values in `vision_config` with the ones in `_vision_config_dict`.
            config.update(_config_dict)

        if config is None:
            config = {}
            logger.info(
                f"`{module_type}_config` is `None`. Initializing the `{config_class.model_type}` with default values."
            )

        return config

    def to_diff_dict(self) -> Dict[str, Any]:
        output = super().to_diff_dict()
        if self.vision_model_type is None:
            output["vision_config"] = None
        if self.vision_projector_type is None:
            output["vision_projector_config"] = None
        if self.audio_model_type is None:
            output["audio_config"] = None
        if self.audio_projector_type is None:
            output["audio_projector_config"] = None
        return output

    def save_to_json(
        self, json_file_path: Union[str, os.PathLike], use_diff: bool = True
    ):
        super().to_json_file(json_file_path, use_diff)

    @classmethod
    def from_json(cls, json_file_path: Union[str, os.PathLike]):
        super().from_json_file(json_file_path)

    def _rope_scaling_validation(self):
        """
        Validate the `rope_scaling` configuration.
        """
        if self.rope_scaling is None:
            return

        if self.rope_scaling is not None and "type" in self.rope_scaling:
            if self.rope_scaling["type"] == "mrope":
                self.rope_scaling["type"] = "default"

    @property
    def num_parameters(self):
        n_ffn = 3 * self.hidden_size * self.intermediate_size

        head_dim = self.hidden_size // self.num_attention_heads
        n_attn = (
            self.hidden_size
            * (self.num_attention_heads + 2 * self.num_key_value_heads)
            * head_dim
        )
        n_attn += self.hidden_size**2
        if self.qkv_bias:
            n_attn += (
                self.num_attention_heads + 2 * self.num_key_value_heads
            ) * head_dim

        n_ln = 2 * self.hidden_size
        n_final_ln = self.hidden_size

        n_embed = self.vocab_size * self.hidden_size
        n_lm_head = self.vocab_size * self.hidden_size

        num_param_dense = self.num_hidden_layers * (n_ffn + n_attn + n_ln) + n_final_ln
        num_param_total = n_embed + n_lm_head + num_param_dense

        return {"total": num_param_total, "dense": num_param_dense}

    def train_flops(self, num_samples: int, seq_len: Optional[int] = None) -> int:
        """Calculate train flops.
        Ignore communication, embedding lookup, rms norm, softmax and grad recompute.

        Args:
            num_samples (int): Num train samples.
            seq_len (Optional[int], optional): Sequence length. Defaults to None.

        Returns:
            int: Approximate train flops.
        """
        seq_len = self.model_max_length if seq_len is None else seq_len
        mlp_flops = 6 * seq_len * self.hidden_size * self.intermediate_size
        qo_flops = 4 * seq_len * self.hidden_size**2
        kv_flops = (
            4
            * seq_len
            * self.num_key_value_heads
            * self.hidden_size**2
            / self.num_attention_heads
        )
        qkvo_flops = qo_flops + kv_flops
        attn_flops = 4 * seq_len**2 * self.hidden_size
        logits_flops = 2 * seq_len * self.hidden_size * self.vocab_size
        forward_flops = num_samples * (
            self.num_hidden_layers * (mlp_flops + qkvo_flops + attn_flops)
            + logits_flops
        )
        backward_flops = forward_flops * 2
        approx_train_flops = forward_flops + backward_flops
        return approx_train_flops
