from dataclasses import dataclass, field
from typing import Optional

from transformers import PretrainedConfig

from mcore_adapter.models import McaModelConfig


@dataclass
class TBStars2MoeVLConfig(McaModelConfig):
    image_token_id: int = 128020
    vision_config: Optional[dict] = field(
        default=None,
        metadata={
            "help": "Vision tower config."
        },
    )
    vision_projector_config: Optional[dict] = field(
        default=None,
        metadata={
            "help": "Vision projector config."
        },
    )

    def __post_init__(self):
        super().__post_init__()
        from .configuration_vista import TBStarsHybridVisionConfig

        if isinstance(self.vision_config, PretrainedConfig):
            self.vision_config = self.vision_config.to_dict()
        if isinstance(self.vision_projector_config, PretrainedConfig):
            self.vision_projector_config = self.vision_projector_config.to_dict()
        vision_config_obj = TBStarsHybridVisionConfig(**self.vision_config)
        self.merge_size = vision_config_obj.spatial_merge_size
        self.pixel_values_dim = (
            vision_config_obj.patch_size
            * vision_config_obj.patch_size
            * vision_config_obj.in_channels
            * vision_config_obj.temporal_patch_size
        )

        assert self.hidden_dropout == 0.0, "hidden dropout is Not supported for tbstars2_moe_vl yet."
