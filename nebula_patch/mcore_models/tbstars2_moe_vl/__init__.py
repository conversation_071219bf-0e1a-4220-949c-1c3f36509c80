from mcore_adapter.models.auto import register_config, register_model
from mcore_adapter.models.converter.dist_converter import (
    DistParallelConfig,
    default_dist_config,
    register_dist_config,
    shared_moe_dist_config,
)
from mcore_adapter.models.converter.template import (
    QKVBiasConverOp,
    QKVConverOp,
    RenameConverOp,
    StackConverOp,
    register_template,
)

from ..mixtbstars.template import MixTBStarsTemplate
from .configuration_tbstars2_moe_vl import TBStars2MoeVLConfig
from .modeling_tbstars2_moe_vl import TBStars2MoeVLModel


__all__ = ["TBStars2MoeVLConfig", "TBStars2MoeVLModel"]

register_config("tbstars2_moe_vista", TBStars2MoeVLConfig)
register_model("tbstars2_moe_vista", TBStars2MoeVLModel)

register_dist_config(
    ["tbstars2_moe_vista"],
    [
        default_dist_config.merge_configs(shared_moe_dist_config),
        DistParallelConfig(module_prefix="vision_tower.", pre_process_weights=["*"], duplicated_weights=["*"]),
        DistParallelConfig(module_prefix="vision_projector.", pre_process_weights=["*"], duplicated_weights=["*"]),
    ],
)

register_template(
    "tbstars2_moe_vista",
    template_class=MixTBStarsTemplate,
    hf_layer_prefix="model.layers.",
    hf_moe_prefix=".moe_mlp.experts.",
    config_hf_to_mca={
        "max_position_embeddings": "max_position_embeddings",
        "hidden_size": "hidden_size",
        "num_attention_heads": "num_attention_heads",
        "num_key_value_heads": "num_query_groups",
        "num_hidden_layers": "num_layers",
        "rms_norm_eps": "layernorm_epsilon",
        "vocab_size": "padded_vocab_size",
        "attention_dropout": "attention_dropout",
        "hidden_dropout": "hidden_dropout",
        "rope_theta": "rotary_base",
        "intermediate_size": "moe_ffn_hidden_size",
        "tie_word_embeddings": "tie_embeddings_and_output_weights",
        # MoE related
        "num_routed_experts": "num_moe_experts",
        "num_experts_per_tok": "moe_router_topk",
        "router_aux_loss_coef": "moe_aux_loss_coeff",
        # vit related
        "image_token_index": "image_token_id",
        "vision_config": "vision_config",
        "vision_projector_config": "vision_projector_config",
    },
    constant_mca_config={
        "swiglu": True,
        "position_embedding_type": "rope",
        "normalization": "RMSNorm",
        "add_bias_linear": False,
        "add_qkv_bias": True,
        "rotary_percent": 1.0,
        "moe_router_load_balancing_type": "aux_loss",
        "moe_use_shared_expert_gate": False,
    },
    weight_converters=[
        RenameConverOp(hf_names="lm_head.weight", mca_names="output_layer.weight"),
        RenameConverOp(hf_names="model.embeddings.weight", mca_names="embedding.word_embeddings.weight"),
        RenameConverOp(hf_names=".norm1.weight", mca_names=".self_attention.linear_qkv.layer_norm_weight"),
        RenameConverOp(hf_names=".attn.o_proj.weight", mca_names=".self_attention.linear_proj.weight"),
        RenameConverOp(hf_names=".norm2.weight", mca_names=".pre_mlp_layernorm.weight"),
        RenameConverOp(hf_names=".down_proj.weight", mca_names=".linear_fc2.weight"),
        RenameConverOp(hf_names="model.norm.weight", mca_names="decoder.final_layernorm.weight"),
        StackConverOp(hf_names=[".up_proj_g.weight", ".up_proj_x.weight"], mca_names=".linear_fc1.weight", dim=0),
        StackConverOp(
            hf_names=[".shared_mlp.up_proj_g.weight", ".shared_mlp.up_proj_x.weight"],
            mca_names=".mlp.shared_experts.linear_fc1.weight",
            dim=0,
        ),
        RenameConverOp(hf_names=".moe_mlp.gate.weight", mca_names=".mlp.router.weight"),
        RenameConverOp(hf_names=".shared_mlp.down_proj.weight", mca_names=".mlp.shared_experts.linear_fc2.weight"),
        QKVConverOp(
            hf_names=[".attn.q_proj.weight", ".attn.k_proj.weight", ".attn.v_proj.weight"],
            mca_names=".self_attention.linear_qkv.weight",
        ),
        QKVBiasConverOp(
            hf_names=[".attn.q_proj.bias", ".attn.k_proj.bias", ".attn.v_proj.bias"],
            mca_names=".self_attention.linear_qkv.bias",
        ),
        RenameConverOp(hf_names="vision_tower.{}", mca_names="vision_tower.{}"),
        RenameConverOp(hf_names="vision_projector.{}", mca_names="vision_projector.{}"),
    ],
)
