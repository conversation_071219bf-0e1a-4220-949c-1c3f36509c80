from typing import List, Optional

import torch
import torch.nn.functional as F
from megatron.core import mpu

from mcore_adapter.models import McaGPTModel, ModuleUtilsMixin

from .configuration_tbstars2_moe_vl import TBStars2MoeVLConfig


class TBStars2MoeVLModel(McaGPTModel, ModuleUtilsMixin):
    config_class = TBStars2MoeVLConfig

    def __init__(self, config: TBStars2MoeVLConfig, **kwargs):
        from .configuration_vista import (
            TBStarsGridPerceiverConfig,
            TBStarsHybridVisionConfig,
        )
        from .modeling_vista import TBStarsHybridGridPerceiver, TBStarsHybridVisionTower

        super().__init__(config, **kwargs)
        self.pre_process = kwargs.get("pre_process", mpu.is_pipeline_first_stage())
        if self.pre_process:
            self.vision_tower = TBStarsHybridVisionTower._from_config(
                TBStarsHybridVisionConfig(**config.vision_config),
                attn_implementation="flash_attention_2",
                torch_dtype=self.config.params_dtype,
            ).to(torch.cuda.current_device())
            for param in self.vision_tower.parameters():
                setattr(param, "sequence_parallel", config.sequence_parallel)

            self.vision_projector = TBStarsHybridGridPerceiver(
                TBStarsGridPerceiverConfig(**config.vision_projector_config)
            ).to(device=torch.cuda.current_device(), dtype=self.config.params_dtype)
            for param in self.vision_projector.parameters():
                setattr(param, "sequence_parallel", config.sequence_parallel)

    def _handle_missing_visual(self, inputs_embeds: "torch.FloatTensor"):
        mock_pixel_values = torch.zeros(
            4,
            self.config.pixel_values_dim,
            device=inputs_embeds.device,
            dtype=inputs_embeds.dtype,
        )
        mock_grid_thw = torch.LongTensor([[1, 2, 2]]).to(inputs_embeds.device)
        image_embeddings = self.get_image_features(mock_pixel_values, grid_thw=mock_grid_thw)
        inputs_embeds = inputs_embeds + image_embeddings.mean() * 0
        return inputs_embeds

    def get_window_index(self, grid_thw: "torch.LongTensor"):
        window_index: list = []
        cu_window_seqlens: list = [0]
        window_index_id = 0

        vision_config = self.config.vision_config
        spatial_merge_size = vision_config["spatial_merge_size"]
        patch_size, window_size = (
            vision_config["patch_size"],
            vision_config["window_size"],
        )
        vit_merger_window_size = window_size // spatial_merge_size // patch_size

        for grid_t, grid_h, grid_w in grid_thw:
            llm_grid_h, llm_grid_w = (
                grid_h // spatial_merge_size,
                grid_w // spatial_merge_size,
            )
            index = torch.arange(grid_t * llm_grid_h * llm_grid_w).reshape(
                grid_t, llm_grid_h, llm_grid_w
            )
            pad_h = vit_merger_window_size - llm_grid_h % vit_merger_window_size
            pad_w = vit_merger_window_size - llm_grid_w % vit_merger_window_size
            num_windows_h = (llm_grid_h + pad_h) // vit_merger_window_size
            num_windows_w = (llm_grid_w + pad_w) // vit_merger_window_size
            index_padded = F.pad(index, (0, pad_w, 0, pad_h), "constant", -100)
            index_padded = index_padded.reshape(
                grid_t,
                num_windows_h,
                vit_merger_window_size,
                num_windows_w,
                vit_merger_window_size,
            )
            index_padded = index_padded.permute(0, 1, 3, 2, 4).reshape(
                grid_t,
                num_windows_h * num_windows_w,
                vit_merger_window_size,
                vit_merger_window_size,
            )
            seqlens = (index_padded != -100).sum([2, 3]).reshape(-1)
            index_padded = index_padded.reshape(-1)
            index_new = index_padded[index_padded != -100]
            window_index.append(index_new + window_index_id)
            cu_seqlens_tmp = (
                seqlens.cumsum(0) * (spatial_merge_size**2) + cu_window_seqlens[-1]
            )
            cu_window_seqlens.extend(cu_seqlens_tmp.tolist())
            window_index_id += (grid_t * llm_grid_h * llm_grid_w).item()
        window_index = torch.cat(window_index, dim=0)

        return window_index, cu_window_seqlens

    def get_image_features(self, images: "torch.Tensor", grid_thw: "torch.LongTensor"):
        window_index, cu_window_seqlens = self.get_window_index(grid_thw)
        image_features = self.vision_tower(
            images, grid_thw, window_index, cu_window_seqlens
        )
        image_features = self.vision_projector(image_features, grid_thw, window_index)
        image_features = image_features.view(-1, image_features.shape[-1])
        return image_features

    def construct_inputs_embeds(
        self,
        input_ids: "torch.LongTensor",
        inputs_embeds: "torch.FloatTensor",
        pixel_values: "torch.Tensor",
        grid_thw: "torch.LongTensor",
        input_ranges: List[List[int]],
        media_token_id: int,
    ):
        """
        inputs_embeds: [s, b, h] or [s/tp, b, h] when sequence parallel
        ranges: sequence range
        """
        image_seqlens = torch.repeat_interleave(
            grid_thw[:, 1] * grid_thw[:, 2], grid_thw[:, 0]
        ).cumsum(dim=0, dtype=torch.int32)
        flatten_grid_thw = torch.repeat_interleave(grid_thw, grid_thw[:, 0], dim=0)
        flatten_grid_thw[:, 0] = 1
        image_embeds_seqlens = image_seqlens // (self.config.merge_size**2)
        assert image_seqlens[-1] == pixel_values.shape[0], (
            f"pixel_values.shape[0] {pixel_values.shape[0]} != image_seqlens[-1] {image_seqlens[-1]}"
        )
        assert sum([r[1] - r[0] for r in input_ranges]) == inputs_embeds.shape[0], (
            f"sum of input_ranges {input_ranges} not match inputs_embeds.shape {inputs_embeds.shape}"
        )
        image_mask = input_ids == media_token_id

        valid_image_embeds_nums = []  # indicate the ranges of needed image embeds
        required_pixel_values, required_grid_thws = (
            [],
            [],
        )  # image features input to vision tower
        added_image_indexes = []
        for i in range(image_mask.shape[0]):
            for inputs_start, inputs_end in input_ranges:
                valid_image_embeds_start = image_mask[:i].sum().item()
                valid_image_embeds_start += image_mask[i, :inputs_start].sum().item()
                embeds_num = image_mask[i, inputs_start:inputs_end].sum().item()
                valid_image_embeds_end = valid_image_embeds_start + embeds_num
                used_embeds_seqlen_start = 0  # embeds seqlens used in this range
                new_embeds_seqlen_start = 0  # embeds seqlens new added in this range, new_embeds_seqlen_start >= used_embeds_seqlen_start
                embeds_seqlen_end = image_embeds_seqlens[-1]
                added_seqlen_before_used = 0
                for image_index, image_embeds_seqlen in enumerate(image_embeds_seqlens):
                    if valid_image_embeds_start < image_embeds_seqlen:
                        if image_index not in added_image_indexes:
                            required_grid_thws.append(flatten_grid_thw[image_index])
                            added_image_indexes.append(image_index)
                        else:
                            new_embeds_seqlen_start = image_embeds_seqlen
                    else:
                        used_embeds_seqlen_start = image_embeds_seqlen
                        new_embeds_seqlen_start = image_embeds_seqlen
                        if image_index in added_image_indexes:
                            before_seqlen = (
                                0
                                if image_index == 0
                                else image_embeds_seqlens[image_index - 1].item()
                            )
                            added_seqlen_before_used += (
                                image_embeds_seqlen - before_seqlen
                            )
                    if valid_image_embeds_end <= image_embeds_seqlen:
                        embeds_seqlen_end = image_embeds_seqlen
                        break

                if new_embeds_seqlen_start < embeds_seqlen_end:
                    required_pixel_values.append(
                        pixel_values[
                            new_embeds_seqlen_start
                            * (self.config.merge_size**2) : embeds_seqlen_end
                            * (self.config.merge_size**2)
                        ]
                    )
                embeds_needed_start = (
                    valid_image_embeds_start
                    - used_embeds_seqlen_start
                    + added_seqlen_before_used
                )
                embeds_needed_end = (
                    valid_image_embeds_end
                    - used_embeds_seqlen_start
                    + added_seqlen_before_used
                )
                if embeds_needed_start < embeds_needed_end:
                    valid_image_embeds_nums.append(
                        (embeds_needed_start, embeds_needed_end)
                    )

        if len(required_pixel_values) == 0:
            return self._handle_missing_visual(inputs_embeds)

        required_pixel_values = torch.cat(required_pixel_values, dim=0)
        required_grid_thw = torch.stack(required_grid_thws, dim=0)
        vision_model_dtype = self.vision_tower.blocks[0].mlp.down_proj.weight.dtype
        required_pixel_values = required_pixel_values.type(vision_model_dtype)
        image_embeds = self.get_image_features(required_pixel_values, required_grid_thw)
        image_embeds = image_embeds.to(inputs_embeds.device, inputs_embeds.dtype)

        image_mask = torch.cat(
            [
                image_mask[:, inputs_start:inputs_end]
                for inputs_start, inputs_end in input_ranges
            ],
            dim=1,
        )
        needed_image_embeds_num = image_mask.sum().item()
        needed_image_embeds = torch.zeros(
            [needed_image_embeds_num] + list(image_embeds.shape[1:]),
            dtype=inputs_embeds.dtype,
            device=inputs_embeds.device,
        )

        added_num = 0
        for start, end in valid_image_embeds_nums:
            embeds_num = end - start
            needed_image_embeds[added_num : added_num + embeds_num] = image_embeds[
                start:end
            ]
            added_num += embeds_num
        assert added_num == needed_image_embeds_num

        inputs_embeds = inputs_embeds.transpose(0, 1)  # [s, b, h] -> [b, s, h]
        image_mask = image_mask.unsqueeze(-1).expand_as(inputs_embeds)
        inputs_embeds = inputs_embeds.masked_scatter(image_mask, needed_image_embeds)
        inputs_embeds = inputs_embeds.transpose(0, 1).contiguous()
        return inputs_embeds

    def get_input_ranges(self, total_seqlen):
        # context parallel 的计算有问题
        slice_rank, slice_size = 0, 1
        if self.config.sequence_parallel:
            slice_rank = mpu.get_tensor_model_parallel_rank()
            slice_size = mpu.get_tensor_model_parallel_world_size()

        def get_sequence_range(start, end, rank, size):
            return start + (end - start) * rank // size, start + (end - start) * (
                rank + 1
            ) // size

        if self.config.context_parallel_size <= 1:
            return [list(get_sequence_range(0, total_seqlen, slice_rank, slice_size))]
        cp_rank = mpu.get_context_parallel_rank()
        cp_size = mpu.get_context_parallel_world_size()
        left_start = (total_seqlen // cp_size // 2) * cp_rank
        left_end = (total_seqlen // cp_size // 2) * (cp_rank + 1)
        right_start = total_seqlen - left_end
        right_end = total_seqlen - left_start
        slice_len = (left_end - left_start + right_end - right_start) // slice_size
        start = left_start + slice_len * slice_rank
        end = start + slice_len
        if start >= left_end:
            start = start - left_end + right_start
            end = start + slice_len
            return [[start, end]]
        if end <= left_end:
            return [[start, end]]
        end = end - left_end + right_start
        return [[start, left_end], [right_start, end]]

    def forward(
        self,
        input_ids: "torch.Tensor",
        position_ids: Optional["torch.Tensor"] = None,
        attention_mask: Optional["torch.Tensor"] = None,
        decoder_input: Optional["torch.Tensor"] = None,
        labels: Optional["torch.Tensor"] = None,
        images: Optional[torch.FloatTensor] = None,
        patch_position_infos: Optional[torch.LongTensor] = None,
        image_num_patches: Optional[torch.LongTensor] = None,
        image_num_tokens: Optional[torch.LongTensor] = None,
        force_vit_image: Optional[bool] = False,
        **kwargs,
    ) -> "torch.Tensor":
        if position_ids is None and input_ids is not None:
            position_ids = torch.arange(0, input_ids.shape[1], device=input_ids.device)

        cp_batch = {
            "position_ids": position_ids,
            "input_ids": input_ids,
            "attention_mask": attention_mask,
        }
        if self.config.context_parallel_size > 1:
            cp_batch = {
                k: v.clone() if v is not None else None for k, v in cp_batch.items()
            }
            cp_batch = super().get_batch_on_this_cp_rank(
                cp_batch, dim3_keys=["attention_mask", "position_ids"]
            )

        if not self.pre_process or images is None or decoder_input is not None:
            return super().forward(
                decoder_input=decoder_input, labels=labels, **cp_batch, **kwargs
            )

        inputs_ranges = self.get_input_ranges(input_ids.shape[1])

        inputs_embeds = self.embedding(
            input_ids=cp_batch["input_ids"], position_ids=cp_batch["position_ids"]
        )
        if images is not None:
            n_idx = 1 if patch_position_infos.shape[1] == 5 else 0
            grid_thw = torch.cat(
                [
                    patch_position_infos[:, n_idx : n_idx + 1],
                    patch_position_infos[:, -2:],
                ],
                dim=1,
            )
            inputs_embeds = self.construct_inputs_embeds(
                input_ids,
                inputs_embeds,
                images,
                grid_thw,
                inputs_ranges,
                self.config.image_token_id,
            )
        elif force_vit_image:
            inputs_embeds = self._handle_missing_visual(inputs_embeds)
        decoder_input = inputs_embeds

        return super().forward(
            decoder_input=decoder_input, labels=labels, **cp_batch, **kwargs
        )
