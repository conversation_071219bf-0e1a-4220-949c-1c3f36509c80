import os
import shutil
from openlm_hub import push_to_mos, register_ckpt

from roll.utils.logging import get_logger

logger = get_logger()


class OpenlmHubUploader:
    """
    {
        "type": "mos",
        "mos_version_uri": mos_version_uri
    }
    """
    def __init__(self, mos_version_uri=None, *args, **kwargs):
        if mos_version_uri is None:
            mos_version_uri = os.environ.get('_NEBULA_MODEL', None)
            if mos_version_uri is None:
                from openlm_hub import get_mos_version_uri_in_mdl_task
                try:
                    mos_version_uri = get_mos_version_uri_in_mdl_task()
                except Exception as e:
                    logger.error(f"Failed to get mos version uri: {e}")
                    mos_version_uri = None

        assert mos_version_uri is not None, ("Use mos as checkpoint uploader, but can not get mos_version_uri,"
                                             "Check your nebula_model config in submit script.")

        self.mos_version_uri = mos_version_uri
        logger.info(f"use OpenlmHubUploader, MOS version: {self.mos_version_uri}")

    def upload(self, ckpt_id: str, local_state_path: str, **kwargs):
        push_to_mos(
            local_file=local_state_path,
            checkpoint_id=ckpt_id,
            mos_version_uri=self.mos_version_uri,
            **kwargs
        )