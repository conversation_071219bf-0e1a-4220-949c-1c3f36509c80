# register nebula tracker/uploader/downloader/template to ROLL
# import common_io
import os

import nebula_patch.datasets.dataset as dataset
from nebula_patch.tracker.mltracker import MLTracker
from nebula_patch.uploader.openlm_hub import OpenlmHubUploader
import nebula_patch.chat_template.chat_template as chat_template

try:
    from nebula_patch.mcore_models import *
except Exception as e:
    print(f"error: import nebula_patch.mcore_models failed: {e}.")

from openlm_hub import repo_download

from roll.utils.checkpoint_manager import model_download_registry
from roll.utils.tracking import tracker_registry
from roll.utils.upload_utils import uploader_registry

uploader_registry["mos"] = OpenlmHubUploader

model_download_registry["OPENLM_HUB"] = repo_download

tracker_registry['ml_tracker'] = MLTracker

NEBULA_ENVS = {
    "MODEL_DOWNLOAD_TYPE": "OPENLM_HUB",
}

for key, value in NEBULA_ENVS.items():
    if key not in os.environ:
        os.environ[key] = value
