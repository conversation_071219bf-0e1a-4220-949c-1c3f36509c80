from transformers import PreTrainedTokenizer

from nebula_patch.chat_template.prompt import TBSTARTS_LONGCOT_V1_FORMAT
from roll.datasets.chat_template import register_chat_template


@register_chat_template("tbstars2")
def tbstars2_chat_template(tokenizer: "PreTrainedTokenizer", conversation, tools=None, documents=None, **kwargs):
    chat_template = (
        "{% if not add_generation_prompt is defined %}{% set add_generation_prompt = false %}{% endif %}"
        "{% for message in messages %}{{'<|im_start|>' + message['role'] + '\n' + message['content'] + '<|im_end|>' "
        "+ '\n'}}{% endfor %}{% if add_generation_prompt %}{{ '<|im_start|>assistant\n' }}{% endif %}"
    )
    kwargs["tokenize"] = False
    kwargs["add_generation_prompt"] = True
    return tokenizer.apply_chat_template(conversation, tools, documents, chat_template=chat_template, **kwargs)


@register_chat_template(
    "longcot_V3_tbstars", base_format="<|im_start|>user\n{{content}}<|im_end|>\n<|im_start|>assistant\n<think>\n"
)
@register_chat_template("tbstars_longcot_v1", base_format=TBSTARTS_LONGCOT_V1_FORMAT)
@register_chat_template(
    "tbstars_longcot_v2",
    base_format="<|im_start|>system\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.<|im_end|>\n<|im_start|>user\n{{content}}<|im_end|>\n<|im_start|>assistant\n<think>\n",
)
@register_chat_template(
    "tbstars_longcot_v3", base_format="<|im_start|>user\n{{content}}<|im_end|>\n<|im_start|>assistant\n<think>\n"
)
def base_chat_template(tokenizer: "PreTrainedTokenizer", conversation, base_format: str, **kwargs):
    assert len(conversation) == 1 and conversation[0]["role"] == "user"
    return base_format.replace("{{content}}", conversation[0]["content"])

