-r requirements_common.txt

# use openlm datasets for odps/ailake table
datasets==2.21.0

torch==2.5.1.*
torchvision==0.20.1.*
torchaudio==2.5.1.*

# flash-attn
http://nebula-cv.oss-cn-zhangjiakou.aliyuncs.com/docker_image/ppu/sdk_1_4_3/torch_251/flash_attn-2.5.6-cp310-cp310-linux_x86_64.whl

#transformer-engine[pytorch]==1.12.0
deepspeed==0.16.0
# vllm==0.7.3
https://nebula-cv.oss-cn-zhangjiakou.aliyuncs.com/yansong/vllm/vllm073/ppu/vllm-0.7.3%2Bcu123-cp310-cp310-linux_x86_64.whl