-r requirements_common.txt

# use openlm datasets for odps/ailake table
datasets==2.21.0

torch==2.6.0.*
torchvision==0.21.0.*
torchaudio==2.6.0.*

flash-attn

# transformer-engine[pytorch]
deepspeed==0.16.4

# 弹内
# https://nebula-cv.oss-cn-zhangjiakou.aliyuncs.com/yansong/vllm/vllm084/gpu/vllm-0.8.4%2Bcu128-cp310-cp310-linux_x86_64.whl

# multi cloud
https://nebula-cv-hz2.oss-cn-hangzhou-internal.aliyuncs.com/docker_image/vllm/vllm084/vllm-0.8.4%2Bcu128-cp310-cp310-linux_x86_64.whl
