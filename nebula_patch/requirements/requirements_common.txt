ray<=2.46.0,>=2.40.0
numpy<2.0a0,>=1.25
tensordict
sympy==1.13.1
transformers==4.51.1
modelscope
# datasets==3.1.0
tqdm
peft==0.12.0
tyro>=0.5.7
accelerate==0.34.2
pydantic
pytest
loralib
einops
isort
jsonlines
deprecated
trl==0.9.6
pyext
dacite
codetiming
more_itertools

wandb

math-verify
openai

gym
gymnasium[toy-text]
gym_sokoban

hydra-core
omegaconf
latex2sympy2==1.5.4
latex2sympy2_extended==1.10.1
antlr4-python3-runtime==4.9.3

# nebula package
./mcore_adapter
ml_tracker
openlm_hub>=0.0.33
