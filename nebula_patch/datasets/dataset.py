import os

import datasets
from datasets import load_dataset

from roll.datasets.dataset import register_dataset
from roll.utils.logging import get_logger

logger = get_logger()

def encode_function(data_i, tag_key, id_key, prompt_key, response_key):
    encodings = {
        # TODO 添加图片的处理逻辑
        'tag': [tag for tag in data_i[tag_key]],
        'id': [id for id in data_i[id_key]],
        'prompt': [prompt for prompt in data_i[prompt_key]],
        'ground_truth': [gt for gt in data_i[response_key]],
        # 'extracted': [""] * len(data_i[prompt_key]),
        # 'correctness_rate': [0] * len(data_i[prompt_key])
    }
    return encodings


@register_dataset("odps")
def load_odps_dataset(dataset_paths: "DataPaths", data_args: "DataArguments"):
    path_from = 'odps'

    tag_key = data_args.tag
    id_key = data_args.id
    prompt_key = data_args.prompt 
    response_key = data_args.response

    kwargs = {}
    kwargs['streaming'] = False
    kwargs['num_workers'] = 8
    kwargs['world_size'] = 1
    kwargs['split'] = 'train'

    # TODO
    # 1. 可以自定义 dataset 类型
    # 2. system prompt
    keep_features = datasets.Features( # 保留字段
        {
            tag_key: datasets.Value("string"),
            id_key: datasets.Value("string"),
            prompt_key: datasets.Value("string"),
            response_key: datasets.Value("string"),
            # image_key: datasets.Value("string"),
            # "correctness_rate": datasets.Value("float"),
        }
    )
    dataset = load_dataset(
        path=path_from,
        data_files=dataset_paths, 
        # features=features, 
        **kwargs
    )
    remove_columns = list(dataset.features.keys() - keep_features.keys())
    logger.info("remove columns:", remove_columns)

    return dataset.map(
        lambda data_i: encode_function(data_i, tag_key=tag_key, id_key=id_key, prompt_key=prompt_key, response_key=response_key),
        batched=True,
        num_proc=32,
        remove_columns=remove_columns,
        desc="Encoding dataset")


        
@register_dataset("ailake")
def load_ailake_dataset(dataset_paths: "DataPaths", data_args: "DataArguments"):
    path_from = 'ailake'

    tag_key = data_args.tag
    id_key = data_args.id
    prompt_key = data_args.prompt 
    response_key = data_args.response

    kwargs = {}
    kwargs['streaming'] = False
    kwargs['num_workers'] = 8
    kwargs['world_size'] = 1
    kwargs['split'] = 'train'

    # TODO
    # 1. 可以自定义 dataset 类型
    # 2. system prompt
    keep_features = datasets.Features( # 保留字段
        {
            tag_key: datasets.Value("string"),
            id_key: datasets.Value("string"),
            prompt_key: datasets.Value("string"),
            response_key: datasets.Value("string"),
            # image_key: datasets.Value("string"),
            # "correctness_rate": datasets.Value("float"),
        }
    )
    dataset = load_dataset(
        path=path_from,
        data_files=dataset_paths, 
        # features=features, 
        **kwargs
    )
    remove_columns = list(dataset.features.keys() - keep_features.keys())
    logger.info("remove columns:", remove_columns)

    return dataset.map(
        lambda data_i: encode_function(data_i, tag_key=tag_key, id_key=id_key, prompt_key=prompt_key, response_key=response_key),
        batched=True,
        num_proc=32,
        remove_columns=remove_columns,
        desc="Encoding dataset")