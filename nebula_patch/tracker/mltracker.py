from typing import Optional

from roll.utils.tracking import BaseTracker


class MLTracker(BaseTracker):

    def __init__(self, config: dict, **kwargs):
        self.config = config
        project = kwargs.pop("project", None)
        tags = kwargs.pop("tags", None)
        name = kwargs.pop("name", None)
        notes = kwargs.pop("notes", None)
        log_dir = kwargs.pop("log_dir", None)
        import ml_tracker
        self.run = ml_tracker.init(project=project, tags=tags, name=name, notes=notes, dir=log_dir)

        self.run.config.update(config, allow_val_change=True)

    def log(self, values: dict, step: Optional[int], **kwargs):
        self.run.log(values, step=step, **kwargs)

    def finish(self):
        self.run.finish()