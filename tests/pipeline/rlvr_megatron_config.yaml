defaults:
  - ../../examples/config/deepspeed_zero@_here_
  - ../../examples/config/deepspeed_zero2@_here_
  - ../../examples/config/deepspeed_zero3@_here_
  - ../../examples/config/deepspeed_zero3_cpuoffload@_here_

hydra:
  run:
    dir: .
  output_subdir: null

exp_name: "rlvr_megatron_test"
seed: 42
logging_dir: ./output/logs
output_dir: ./output
system_envs:
  USE_MODELSCOPE: '1'

checkpoint_config:
  type: file_system
  output_dir: ./output/checkpoint

#track_with: wandb
#tracker_kwargs:
#  api_key: your_api_key
#  project: roll-rlvr
#  log_dir: debug
#  tags:
#    - roll
#    - rlvr
#    - debug


num_gpus_per_node: 8

max_steps: 500
save_steps: 1000
logging_steps: 1
eval_steps: 10
resume_from_checkpoint: false

rollout_batch_size: 64
prompt_length: 1024
response_length: 512

num_return_sequences_in_group: 8
ppo_epochs: 1
value_clip: 0.5
reward_clip: 10
advantage_clip: 2.0
whiten_advantages: true
init_kl_coef: 0.1
adv_estimator: "reinforce"


is_use_additional_prompts: false
reward_filter_mean_threshold: 0.3
max_running_requests: 256
is_num_return_sequences_expand: false
max_additional_running_prompts: 16
generate_redundancy_num: 0


pretrain: Qwen/Qwen2.5-0.5B-Instruct
reward_pretrain: Qwen/Qwen2.5-0.5B-Instruct

validation:
  data_args:
    template: qwen2_5
    file_name:
      - data/math_benchmarks.jsonl
  generating_args:
    top_p: 0.6
    top_k: 50
    num_beams: 1
    temperature: 0.6
    num_return_sequences: 1
  eval_steps: 10

actor_train:
  model_args:
    dtype: bf16
    model_type: ~
  training_args:
    learning_rate: 1.0e-6
    weight_decay: 0
    per_device_train_batch_size: 4
    gradient_accumulation_steps: 8
    warmup_steps: 50
    num_train_epochs: 50
  data_args:
    template: qwen2_5
    file_name:
      - /data/oss_bucket_0/rl_examples/data/code_KodCode_data_hard.jsonl
#      - /data/oss_bucket_0/rl_examples/data/llm_judge_Multi-subject-RLVR_deal_new.jsonl
      - /data/oss_bucket_0/rl_examples/data/math_deepmath_deal.jsonl
    prompt: instruction
    interleave_probs: "1.0"
    max_samples: 6400
    preprocessing_num_workers: 16
    domain_interleave_probs:
      math_rule: 0.5
      code_sandbox: 0.4
#      llm_judge: 0.1
  strategy_args:
    strategy_name: megatron_train
    strategy_config:
      tensor_model_parallel_size: 2
      pipeline_model_parallel_size: 1
      expert_model_parallel_size: 1
      context_parallel_size: 1
      overlap_grad_reduce: false
      use_distributed_optimizer: true
  device_mapping: list(range(0,8))
  infer_batch_size: 4

actor_infer:
  model_args:
    dtype: bf16
  generating_args:
    max_new_tokens: ${response_length}
    top_p: 0.99
    top_k: 100
    num_beams: 1
    temperature: 0.99
    num_return_sequences: ${num_return_sequences_in_group}
  data_args:
    template: qwen2_5
  strategy_args:
    strategy_name: vllm
    strategy_config:
      gpu_memory_utilization: 0.8
      block_size: 16
      max_model_len: 6000
  device_mapping: list(range(0,7))
  infer_batch_size: 1

reference:
  model_args:
    dtype: bf16
    model_type: ~
  data_args:
    template: qwen2_5
  strategy_args:
    strategy_name: megatron_infer
    strategy_config:
      tensor_model_parallel_size: 1
      pipeline_model_parallel_size: 1
      expert_model_parallel_size: 1
  device_mapping: list(range(0,8))
  infer_batch_size: 8

rewards:
  math_rule:
    worker_cls: roll.pipeline.rlvr.rewards.math_rule_reward_worker.MathRuleRewardWorker
    model_args:
      model_name_or_path: ${reward_pretrain}
    data_args:
      template: qwen2_5
    tag_included: [cn_k12, orca_math, olympiads, gsm8k, math, aops_forum]
    world_size: 8
    infer_batch_size: 1
    query_filter_config:
      type: mean_filter
      filter_args:
        threshold_up: 0.9
        threshold_down: 0.1
  code_sandbox:
    use_local: true
    worker_cls: roll.pipeline.rlvr.rewards.code_sandbox_reward_worker.CodeSandboxRewardWorker
    tag_included: [assert, input, livecodebench_random100]
    model_args:
      model_name_or_path: ${reward_pretrain}
    data_args:
      template: qwen2_5
    world_size: 8
    infer_batch_size: 1
    query_filter_config:
      type: std_filter
      filter_args:
        std_threshold: 0
#  llm_judge:
#    # NOTE: llm as judge 也需要gpu, 不能和actor infer共享gpu
#    worker_cls: roll.pipeline.rlvr.rewards.llm_judge_reward_worker.LLMJudgeRewardWorker
#    judge_prompt: Qwen2.5-7B-Instruct-RLVR-prompt
#    judge_model_type: inference
#    tag_included: [reference]
#    model_args:
#      model_name_or_path: AI-ModelScope/Qwen2.5-7B-Instruct-RLVR
#      attn_implementation: fa2
#      disable_gradient_checkpointing: true
#      dtype: bf16
#      model_type: trl
#    generating_args:
#      max_new_tokens: 100
#      top_p: 0.8
#      top_k: 50
#      num_beams: 1
#      temperature: 0.8
#      num_return_sequences: 1
#    data_args:
#      template: qwen2_5
#    strategy_args:
#      strategy_name: hf_infer
#      strategy_config: null
#    device_mapping: list(range(7,8))
#    infer_batch_size: 1