hydra:
  run:
    dir: .
  output_subdir: null

exp_name: "log_probs_megatron_debug"
seed: 42
logging_dir: ./output/logs
output_dir: ./output

track_with: stdout

rollout_batch_size: 512
prompt_length: 128
response_length: 512

pretrain: Qwen/Qwen2.5-7B-Instruct

actor_infer:
  model_args:
    attn_implementation: fa2
    disable_gradient_checkpointing: true
    dtype: bf16
  generating_args:
    max_new_tokens: ${response_length}
    top_p: 0.99
    top_k: 100
    num_beams: 1
    temperature: 0.99
    num_return_sequences: 1
  data_args:
    template: qwen2_5
    file_name: data/comparison_gpt4_data_zh.json
    dataset_dir: data
    prompt: instruction
    interleave_probs: "1.0"
    preprocessing_num_workers: 16
    max_samples: ${rollout_batch_size}
  strategy_args:
    strategy_name: hf_infer
    strategy_config: ~
  device_mapping: list(range(0,8))
  infer_batch_size: 16

reference:
  model_args:
    disable_gradient_checkpointing: true
    dtype: bf16
  data_args:
    template: qwen2_5
  strategy_args:
    strategy_name: megatron_infer
    strategy_config:
      tensor_model_parallel_size: 1
      pipeline_model_parallel_size: 1
      expert_model_parallel_size: 1
  device_mapping: list(range(0,8))
  infer_batch_size: 4
