hydra:
  run:
    dir: .
  output_subdir: null

exp_name: "megatron_train_checkpoint_debug"
seed: 42
logging_dir: ./output/logs
output_dir: ./output

checkpoint_config:
  type: file_system
  output_dir: /data/cpfs_0/xiongshaopan.xsp/models/${exp_name}

track_with: stdout

save_steps: 1
rollout_batch_size: 512
prompt_length: 128
response_length: 512
#resume_from_checkpoint: /data/cpfs_0/xiongshaopan.xsp/models/megatron_0.5B_ckpt/checkpoint-0
#resume_from_checkpoint: /data/cpfs_0/xiongshaopan.xsp/models/megatron_0.5B_async_ckpt/checkpoint-1
#resume_from_checkpoint: /data/cpfs_0/xiongshaopan.xsp/models/megatron_train_checkpoint_debug/20250305-172118/checkpoint-1
resume_from_checkpoint: /data/cpfs_0/xiongshaopan.xsp/models/megatron_train_checkpoint_debug/20250305-212054/checkpoint-1
pretrain: /data/cpfs_0/common/models/Qwen2.5-0.5B-Instruct

actor_train:
  model_args:
    disable_gradient_checkpointing: false
    dtype: bf16
    model_type: ~
  training_args:
    learning_rate: 5.0e-7
    weight_decay: 0
    per_device_train_batch_size: 4
    gradient_accumulation_steps: 8
    warmup_steps: 5
    num_train_epochs: 1
  data_args:
    template: qwen2_5
    file_name: data/comparison_gpt4_data_zh.json
    dataset_dir: data
    prompt: instruction
    interleave_probs: "1.0"
    max_samples: 4096
  strategy_args:
    strategy_name: megatron_train
    strategy_config:
      tensor_model_parallel_size: 2
      pipeline_model_parallel_size: 1
      expert_model_parallel_size: 1
      context_parallel_size: 2
      overlap_grad_reduce: false
      use_distributed_optimizer: true
  device_mapping: list(range(0,8))

actor_infer:
  generating_args:
    num_return_sequences: 1