
hydra:
  run:
    dir: .
  output_subdir: null

exp_name: "model_update_baseline"
seed: 42
logging_dir: ./output/logs
output_dir: ./output

track_with: stdout

rollout_batch_size: 1024
prompt_length: 1024
response_length: 1024


pretrain: Qwen/Qwen2.5-7B-Instruct

actor_train:
  model_args:
    dtype: bf16
    model_type: ~
  data_args:
    template: qwen2_5
    file_name: data/comparison_gpt4_data_zh.json
    dataset_dir: data
    prompt: instruction
    interleave_probs: "1.0"
  training_args:
    learning_rate: 5.0e-7
    weight_decay: 0
    per_device_train_batch_size: 1
    gradient_accumulation_steps: 2
    warmup_ratio: 0.1
    num_train_epochs: 1
  strategy_args:
    strategy_name: megatron_train
    strategy_config:
      tensor_model_parallel_size: 2
      pipeline_model_parallel_size: 2
      expert_model_parallel_size: 1
      context_parallel_size: 1
      overlap_grad_reduce: false
      use_distributed_optimizer: true
#    strategy_name: deepspeed_train
#    strategy_config:
#      train_micro_batch_size_per_gpu: auto
#      bf16:
#        enabled: true
#      zero_optimization:
#        stage: 3
  device_mapping: list(range(0,4))


actor_infer:
  model_args:
    dtype: bf16
  generating_args:
    max_new_tokens: ${response_length}
    top_p: 0.99
    top_k: 100
    num_beams: 1
    temperature: 0.99
    num_return_sequences: 1
  data_args:
    template: qwen2_5
  strategy_args:
    strategy_name: vllm
    strategy_config:
      gpu_memory_utilization: 0.8
      block_size: 16
      max_model_len: 6000
      tensor_parallel_size: 2
#    strategy_name: hf_infer
#    strategy_config: ~
  device_mapping: list(range(0,8))
